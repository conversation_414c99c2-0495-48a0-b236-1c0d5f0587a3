# WebDAV实时同步使用指南

本指南介绍如何配置和使用WebDAV实时同步功能，将MySQL备份文件自动同步到云端存储。

## 🌟 支持的WebDAV服务

### 1. 坚果云（推荐）
- **优势**：国内访问速度快，稳定性好，免费版有3GB空间
- **配置简单**：支持标准WebDAV协议
- **获取信息**：
  1. 登录坚果云网页版
  2. 进入"账户信息" -> "安全选项" -> "第三方应用管理"
  3. 添加应用，生成应用密码

```bash
WEBDAV_URL="https://dav.jianguoyun.com/dav/"
WEBDAV_USERNAME="<EMAIL>"
WEBDAV_PASSWORD="your_app_password"
```

### 2. OneDrive
- **优势**：微软官方服务，5GB免费空间
- **注意**：需要SharePoint或OneDrive for Business
- **配置复杂度**：中等

```bash
WEBDAV_URL="https://your-tenant.sharepoint.com/sites/your-site/_vti_bin/"
WEBDAV_USERNAME="<EMAIL>"
WEBDAV_PASSWORD="your_password"
```

### 3. Nextcloud
- **优势**：开源，功能强大，可自建
- **适用场景**：有自己服务器的用户

```bash
WEBDAV_URL="https://your-domain.com/nextcloud/remote.php/dav/files/username/"
WEBDAV_USERNAME="your_username"
WEBDAV_PASSWORD="your_password"
```

### 4. ownCloud
- **优势**：企业级云存储解决方案
- **适用场景**：企业用户

```bash
WEBDAV_URL="https://your-domain.com/owncloud/remote.php/webdav/"
WEBDAV_USERNAME="your_username"
WEBDAV_PASSWORD="your_password"
```

### 5. 群晖NAS
- **优势**：本地存储，速度快，安全性高
- **适用场景**：有群晖NAS的用户

```bash
WEBDAV_URL="https://your-nas-ip:5006/"
WEBDAV_USERNAME="your_nas_username"
WEBDAV_PASSWORD="your_nas_password"
```

## 🚀 快速部署

### 方法一：一键部署（推荐）

```bash
# 下载并运行包含WebDAV的部署脚本
chmod +x deploy_mysql_backup_with_webdav.sh
./deploy_mysql_backup_with_webdav.sh
```

### 方法二：手动配置

1. **部署基础备份系统**
```bash
./deploy_mysql_backup.sh
```

2. **配置WebDAV同步**
```bash
# 复制WebDAV脚本
cp webdav_sync.sh /www/backup/scripts/
cp webdav_config.conf /www/backup/scripts/
chmod +x /www/backup/scripts/webdav_sync.sh

# 编辑配置文件
vi /www/backup/scripts/webdav_sync.sh
```

3. **修改配置参数**
```bash
WEBDAV_URL="your_webdav_url"
WEBDAV_USERNAME="your_username"
WEBDAV_PASSWORD="your_password"
WEBDAV_REMOTE_DIR="mysql_backup"
LOCAL_BACKUP_DIR="/www/backup/mysql"
```

4. **测试连接**
```bash
/www/backup/scripts/webdav_sync.sh test
```

5. **启动同步服务**
```bash
# 创建systemd服务
cat > /etc/systemd/system/webdav-sync.service << EOF
[Unit]
Description=WebDAV Real-time Sync Service
After=network.target mysql-realtime-backup.service

[Service]
Type=simple
User=root
ExecStart=/www/backup/scripts/webdav_sync.sh start
ExecStop=/www/backup/scripts/webdav_sync.sh stop
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
systemctl daemon-reload
systemctl enable webdav-sync
systemctl start webdav-sync
```

## 🔧 配置详解

### 基本配置参数

```bash
# WebDAV服务器信息
WEBDAV_URL="https://your-server.com/dav/"     # WebDAV服务器地址
WEBDAV_USERNAME="your_username"               # 用户名
WEBDAV_PASSWORD="your_password"               # 密码或应用密码
WEBDAV_REMOTE_DIR="mysql_backup"              # 远程目录名

# 本地配置
LOCAL_BACKUP_DIR="/www/backup/mysql"          # 本地备份目录

# 同步配置
SYNC_INTERVAL=60                               # 定时同步间隔（秒）
MAX_RETRY_COUNT=3                              # 最大重试次数
RETRY_DELAY=10                                 # 重试延迟（秒）
BANDWIDTH_LIMIT=0                              # 带宽限制（KB/s，0=不限制）
```

### 高级配置

```bash
# 日志配置
LOG_FILE="/var/log/webdav_sync.log"
ERROR_LOG="/var/log/webdav_sync_error.log"

# 进程管理
PID_FILE="/var/run/webdav_sync.pid"

# 性能优化
ENABLE_COMPRESSION=true                        # 启用压缩
COMPRESSION_LEVEL=6                            # 压缩级别（1-9）
```

## 📊 管理命令

### 服务管理

```bash
# 查看服务状态
systemctl status webdav-sync

# 启动/停止/重启服务
systemctl start webdav-sync
systemctl stop webdav-sync
systemctl restart webdav-sync

# 查看服务日志
journalctl -u webdav-sync -f
```

### 手动操作

```bash
# 测试WebDAV连接
/www/backup/scripts/webdav_sync.sh test

# 执行一次全量同步
/www/backup/scripts/webdav_sync.sh sync

# 查看同步状态
/www/backup/scripts/webdav_sync.sh status

# 初始化WebDAV环境
/www/backup/scripts/webdav_sync.sh init

# 启动/停止实时监控
/www/backup/scripts/webdav_sync.sh start
/www/backup/scripts/webdav_sync.sh stop
```

## 📈 监控和日志

### 查看日志

```bash
# 实时查看同步日志
tail -f /var/log/webdav_sync.log

# 查看错误日志
tail -f /var/log/webdav_sync_error.log

# 查看最近的同步记录
grep "上传成功" /var/log/webdav_sync.log | tail -10
```

### 监控指标

- **同步成功率**：成功上传的文件数量 / 总文件数量
- **同步延迟**：文件变化到上传完成的时间
- **错误率**：上传失败的文件数量 / 总文件数量
- **带宽使用**：上传数据量和速度

## 🔍 故障排除

### 常见问题

1. **连接超时**
```bash
# 检查网络连接
ping your-webdav-server.com

# 检查防火墙
iptables -L | grep -i drop

# 测试端口连通性
telnet your-webdav-server.com 443
```

2. **认证失败**
```bash
# 验证用户名密码
curl -u "username:password" https://your-webdav-server.com/dav/

# 检查应用密码（坚果云等）
# 确保使用的是应用密码而不是登录密码
```

3. **上传失败**
```bash
# 检查磁盘空间
df -h

# 检查文件权限
ls -la /www/backup/mysql/

# 查看详细错误信息
tail -50 /var/log/webdav_sync_error.log
```

4. **同步延迟**
```bash
# 检查inotify是否正常工作
ps aux | grep inotifywait

# 调整同步间隔
vi /www/backup/scripts/webdav_sync.sh
# 修改 SYNC_INTERVAL 参数
```

### 性能优化

1. **带宽限制**
```bash
# 限制上传速度为500KB/s
BANDWIDTH_LIMIT=500
```

2. **压缩优化**
```bash
# 启用压缩并调整压缩级别
ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=6  # 1-9，数字越大压缩率越高但CPU占用越多
```

3. **并发控制**
```bash
# 避免同时上传多个大文件
# 脚本已内置队列机制，无需额外配置
```

## 🔐 安全建议

### 1. 密码安全
- 使用应用专用密码而不是主账户密码
- 定期更换密码
- 避免在脚本中明文存储密码

### 2. 网络安全
- 使用HTTPS连接
- 配置防火墙规则
- 考虑使用VPN

### 3. 访问控制
- 限制WebDAV用户权限
- 定期审查访问日志
- 设置IP白名单（如果支持）

## 📋 最佳实践

### 1. 目录结构
```
WebDAV根目录/
└── mysql_backup/
    ├── full/           # 全量备份
    ├── incremental/    # 增量备份
    └── binlog/         # 二进制日志备份
```

### 2. 备份策略
- **实时同步**：文件变化时立即上传
- **定时全量同步**：每小时检查一次完整性
- **错误重试**：失败时自动重试3次

### 3. 监控告警
```bash
# 添加监控脚本到crontab
*/10 * * * * /www/backup/scripts/check_webdav_sync.sh
```

### 4. 容量管理
- 定期清理过期备份文件
- 监控WebDAV存储空间使用情况
- 设置自动清理策略

## 📞 技术支持

如果遇到问题，请按以下步骤排查：

1. **检查日志文件**：查看详细错误信息
2. **测试连接**：运行连接测试命令
3. **验证配置**：确认WebDAV参数正确
4. **网络检查**：确保网络连通性
5. **权限验证**：检查文件和目录权限

常用调试命令：
```bash
# 启用详细日志
export DEBUG=1
/www/backup/scripts/webdav_sync.sh test

# 手动测试上传
curl -T test_file.txt -u "username:password" https://your-webdav-server.com/dav/test_file.txt
```
