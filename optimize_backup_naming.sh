#!/bin/bash
# 优化备份命名策略脚本

echo "=========================================="
echo "优化MySQL备份命名策略"
echo "=========================================="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${YELLOW}[STEP]${NC} $1"
}

# 备份原始脚本
log_step "备份原始脚本..."
cp /www/backup/scripts/mysql_realtime_backup.sh /www/backup/scripts/mysql_realtime_backup.sh.backup
log_info "原始脚本已备份到: mysql_realtime_backup.sh.backup"

# 修改全量备份函数
log_step "修改全量备份命名策略..."

# 创建新的全量备份函数
cat > /tmp/new_full_backup_function.txt << 'EOF'
# 全量备份函数（按周几命名）
full_backup() {
    log_message "INFO" "开始执行全量备份..."
    
    # 获取星期几（1=周一, 7=周日）
    local weekday=$(date +%u)
    local weekday_name=""
    
    case $weekday in
        1) weekday_name="Monday" ;;
        2) weekday_name="Tuesday" ;;
        3) weekday_name="Wednesday" ;;
        4) weekday_name="Thursday" ;;
        5) weekday_name="Friday" ;;
        6) weekday_name="Saturday" ;;
        7) weekday_name="Sunday" ;;
    esac
    
    # 按周几命名备份文件
    local backup_file="$FULL_BACKUP_DIR/full_backup_${weekday_name}_$(date +%Y%m%d_%H%M%S).sql"
    local start_time=$(date +%s)
    
    # 删除同一天的旧备份（保持最新）
    find "$FULL_BACKUP_DIR" -name "full_backup_${weekday_name}_*.sql*" -type f -delete 2>/dev/null
    
    # 执行mysqldump
    mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --master-data=2 \
        --flush-logs \
        --hex-blob \
        --default-character-set=utf8mb4 \
        $DB_NAME > "$backup_file" 2>>"$ERROR_LOG"
    
    local dump_result=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $dump_result -eq 0 ]; then
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_message "INFO" "全量备份完成: $backup_file (大小: $file_size, 耗时: ${duration}秒)"
        
        # 压缩备份文件
        if [ "$ENABLE_COMPRESSION" = true ]; then
            gzip -$COMPRESSION_LEVEL "$backup_file"
            log_message "INFO" "备份文件已压缩: ${backup_file}.gz"
        fi
        
        # 记录备份位置信息
        local position_file="$FULL_BACKUP_DIR/last_full_backup_position.txt"
        mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" > "$position_file"
        
        return 0
    else
        log_message "ERROR" "全量备份失败，请检查错误日志: $ERROR_LOG"
        return 1
    fi
}
EOF

# 查找并替换全量备份函数
log_step "应用新的备份函数..."

# 使用sed替换全量备份函数
sed -i '/^# 全量备份函数$/,/^}$/c\
# 全量备份函数（按周几命名）\
full_backup() {\
    log_message "INFO" "开始执行全量备份..."\
    \
    # 获取星期几（1=周一, 7=周日）\
    local weekday=$(date +%u)\
    local weekday_name=""\
    \
    case $weekday in\
        1) weekday_name="Monday" ;;\
        2) weekday_name="Tuesday" ;;\
        3) weekday_name="Wednesday" ;;\
        4) weekday_name="Thursday" ;;\
        5) weekday_name="Friday" ;;\
        6) weekday_name="Saturday" ;;\
        7) weekday_name="Sunday" ;;\
    esac\
    \
    # 按周几命名备份文件\
    local backup_file="$FULL_BACKUP_DIR/full_backup_${weekday_name}_$(date +%Y%m%d_%H%M%S).sql"\
    local start_time=$(date +%s)\
    \
    # 删除同一天的旧备份（保持最新）\
    find "$FULL_BACKUP_DIR" -name "full_backup_${weekday_name}_*.sql*" -type f -delete 2>/dev/null\
    \
    # 执行mysqldump\
    mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS \\\
        --single-transaction \\\
        --routines \\\
        --triggers \\\
        --events \\\
        --master-data=2 \\\
        --flush-logs \\\
        --hex-blob \\\
        --default-character-set=utf8mb4 \\\
        $DB_NAME > "$backup_file" 2>>"$ERROR_LOG"\
    \
    local dump_result=$?\
    local end_time=$(date +%s)\
    local duration=$((end_time - start_time))\
    \
    if [ $dump_result -eq 0 ]; then\
        local file_size=$(du -h "$backup_file" | cut -f1)\
        log_message "INFO" "全量备份完成: $backup_file (大小: $file_size, 耗时: ${duration}秒)"\
        \
        # 压缩备份文件\
        if [ "$ENABLE_COMPRESSION" = true ]; then\
            gzip -$COMPRESSION_LEVEL "$backup_file"\
            log_message "INFO" "备份文件已压缩: ${backup_file}.gz"\
        fi\
        \
        # 记录备份位置信息\
        local position_file="$FULL_BACKUP_DIR/last_full_backup_position.txt"\
        mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" > "$position_file"\
        \
        return 0\
    else\
        log_message "ERROR" "全量备份失败，请检查错误日志: $ERROR_LOG"\
        return 1\
    fi\
}' /www/backup/scripts/mysql_realtime_backup.sh

# 修改清理函数，只保留7天的备份
log_step "修改清理策略..."

sed -i 's/FULL_BACKUP_RETENTION_DAYS=30/FULL_BACKUP_RETENTION_DAYS=7/' /www/backup/scripts/mysql_realtime_backup.sh
sed -i 's/INCREMENTAL_RETENTION_DAYS=7/INCREMENTAL_RETENTION_DAYS=3/' /www/backup/scripts/mysql_realtime_backup.sh

# 修改清理函数以支持新的命名格式
sed -i '/find "$FULL_BACKUP_DIR" -name "full_backup_\*.sql\*"/c\
    # 清理7天前的全量备份（按周几命名）\
    find "$FULL_BACKUP_DIR" -name "full_backup_*_*.sql*" -mtime +$FULL_BACKUP_RETENTION_DAYS -delete' /www/backup/scripts/mysql_realtime_backup.sh

# 创建增量备份优化函数
log_step "优化增量备份策略..."

# 修改增量备份保留策略
cat > /tmp/cleanup_incremental.sh << 'EOF'
#!/bin/bash
# 清理增量备份，只保留最近3天的

INCREMENTAL_DIR="/www/backup/mysql/incremental"
RETENTION_DAYS=3

# 删除3天前的增量备份
find "$INCREMENTAL_DIR" -name "incremental_*.sql*" -mtime +$RETENTION_DAYS -delete

# 如果增量备份文件过多（超过50个），只保留最新的30个
INCREMENTAL_COUNT=$(ls -1 "$INCREMENTAL_DIR"/incremental_*.sql* 2>/dev/null | wc -l)
if [ $INCREMENTAL_COUNT -gt 50 ]; then
    ls -t "$INCREMENTAL_DIR"/incremental_*.sql* | tail -n +31 | xargs rm -f
fi

echo "$(date): 增量备份清理完成，当前文件数: $(ls -1 "$INCREMENTAL_DIR"/incremental_*.sql* 2>/dev/null | wc -l)" >> /var/log/mysql_backup.log
EOF

chmod +x /tmp/cleanup_incremental.sh
mv /tmp/cleanup_incremental.sh /www/backup/scripts/

# 修改定时任务
log_step "优化定时任务..."

# 备份当前定时任务
crontab -l > /tmp/current_cron.backup

# 创建新的定时任务
cat > /tmp/new_cron << 'EOF'
# MySQL实时备份定时任务（优化版）
0 2 * * * /www/backup/scripts/mysql_realtime_backup.sh full >/dev/null 2>&1
0 */6 * * * /www/backup/scripts/mysql_realtime_backup.sh binlog >/dev/null 2>&1
0 3 * * * /www/backup/scripts/mysql_realtime_backup.sh cleanup >/dev/null 2>&1
30 3 * * * /www/backup/scripts/cleanup_incremental.sh >/dev/null 2>&1
EOF

# 保留其他非备份相关的定时任务
grep -v "mysql_realtime_backup.sh" /tmp/current_cron.backup >> /tmp/new_cron

# 应用新的定时任务
crontab /tmp/new_cron

log_info "定时任务已更新"

# 测试新的备份功能
log_step "测试新的备份命名..."

# 执行一次全量备份测试
/www/backup/scripts/mysql_realtime_backup.sh full

# 显示结果
echo ""
echo "=========================================="
log_info "优化完成！"
echo "=========================================="
echo ""
echo "新的备份命名格式："
echo "  全量备份: full_backup_Monday_20250813_171801.sql.gz"
echo "  增量备份: incremental_20250813_171801.sql.gz (保持原格式)"
echo ""
echo "备份保留策略："
echo "  全量备份: 7天 (每个星期几保留最新的一个)"
echo "  增量备份: 3天 (最多保留50个文件)"
echo "  Binlog备份: 3天"
echo ""
echo "当前备份文件："
ls -la /www/backup/mysql/full/
echo ""
echo "定时任务："
echo "  每日02:00 - 全量备份"
echo "  每6小时 - Binlog备份"  
echo "  每日03:00 - 清理旧备份"
echo "  每日03:30 - 清理增量备份"
echo ""
log_info "优化完成！现在备份文件按周几命名，方便查看和管理。"
