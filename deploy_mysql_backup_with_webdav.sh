#!/bin/bash
# MySQL实时备份系统部署脚本（包含WebDAV同步）
# 适用于宝塔面板 + MySQL 5.6.50 + WebDAV

echo "=========================================="
echo "MySQL实时备份系统部署脚本（含WebDAV同步）"
echo "适用于宝塔面板 + MySQL 5.6.50"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查宝塔面板
check_bt_panel() {
    if [ ! -f "/etc/init.d/bt" ]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    log_info "检测到宝塔面板"
}

# 检查MySQL
check_mysql() {
    if ! command -v mysql &> /dev/null; then
        log_error "未检测到MySQL，请先在宝塔面板中安装MySQL"
        exit 1
    fi
    
    local mysql_version=$(mysql --version | grep -oP '\d+\.\d+\.\d+' | head -1)
    log_info "检测到MySQL版本: $mysql_version"
}

# 安装依赖
install_dependencies() {
    log_step "安装必要依赖..."
    
    # 检查并安装curl
    if ! command -v curl &> /dev/null; then
        log_info "安装curl..."
        yum install -y curl
    fi
    
    # 检查并安装inotify-tools
    if ! command -v inotifywait &> /dev/null; then
        log_info "安装inotify-tools..."
        yum install -y inotify-tools
    fi
    
    # 检查并安装rsync
    if ! command -v rsync &> /dev/null; then
        log_info "安装rsync..."
        yum install -y rsync
    fi
    
    log_info "依赖安装完成"
}

# 获取用户输入
get_user_input() {
    echo ""
    echo "请输入以下配置信息："
    
    # 数据库信息
    log_step "数据库配置"
    read -p "MySQL root密码: " -s MYSQL_ROOT_PASS
    echo ""
    read -p "要备份的数据库名: " DB_NAME
    read -p "备份用户名 (默认: backup_user): " BACKUP_USER
    BACKUP_USER=${BACKUP_USER:-backup_user}
    read -p "备份用户密码: " -s BACKUP_PASS
    echo ""
    
    # 备份目录
    log_step "备份目录配置"
    read -p "备份目录 (默认: /www/backup): " BACKUP_DIR
    BACKUP_DIR=${BACKUP_DIR:-/www/backup}
    
    # WebDAV配置
    log_step "WebDAV配置"
    read -p "是否启用WebDAV同步? (y/n, 默认: y): " ENABLE_WEBDAV
    ENABLE_WEBDAV=${ENABLE_WEBDAV:-y}
    
    if [ "$ENABLE_WEBDAV" = "y" ] || [ "$ENABLE_WEBDAV" = "Y" ]; then
        echo ""
        echo "常见WebDAV服务："
        echo "1. 坚果云 (推荐)"
        echo "2. OneDrive"
        echo "3. Nextcloud"
        echo "4. ownCloud"
        echo "5. 群晖NAS"
        echo "6. 自定义"
        echo ""
        read -p "请选择WebDAV服务类型 (1-6): " WEBDAV_TYPE
        
        case $WEBDAV_TYPE in
            1)
                log_info "配置坚果云WebDAV..."
                WEBDAV_URL="https://dav.jianguoyun.com/dav/"
                read -p "坚果云邮箱: " WEBDAV_USERNAME
                echo "请在坚果云网页版 -> 账户信息 -> 安全选项 -> 第三方应用管理 中生成应用密码"
                read -p "坚果云应用密码: " -s WEBDAV_PASSWORD
                echo ""
                ;;
            2)
                log_info "配置OneDrive WebDAV..."
                read -p "OneDrive WebDAV URL: " WEBDAV_URL
                read -p "OneDrive用户名: " WEBDAV_USERNAME
                read -p "OneDrive密码: " -s WEBDAV_PASSWORD
                echo ""
                ;;
            3)
                log_info "配置Nextcloud WebDAV..."
                read -p "Nextcloud服务器地址 (如: https://cloud.example.com): " NEXTCLOUD_SERVER
                read -p "Nextcloud用户名: " WEBDAV_USERNAME
                WEBDAV_URL="$NEXTCLOUD_SERVER/remote.php/dav/files/$WEBDAV_USERNAME/"
                read -p "Nextcloud密码: " -s WEBDAV_PASSWORD
                echo ""
                ;;
            4)
                log_info "配置ownCloud WebDAV..."
                read -p "ownCloud服务器地址 (如: https://cloud.example.com): " OWNCLOUD_SERVER
                read -p "ownCloud用户名: " WEBDAV_USERNAME
                WEBDAV_URL="$OWNCLOUD_SERVER/remote.php/webdav/"
                read -p "ownCloud密码: " -s WEBDAV_PASSWORD
                echo ""
                ;;
            5)
                log_info "配置群晖NAS WebDAV..."
                read -p "群晖NAS IP地址: " NAS_IP
                WEBDAV_URL="https://$NAS_IP:5006/"
                read -p "群晖用户名: " WEBDAV_USERNAME
                read -p "群晖密码: " -s WEBDAV_PASSWORD
                echo ""
                ;;
            6)
                log_info "配置自定义WebDAV..."
                read -p "WebDAV服务器URL: " WEBDAV_URL
                read -p "WebDAV用户名: " WEBDAV_USERNAME
                read -p "WebDAV密码: " -s WEBDAV_PASSWORD
                echo ""
                ;;
            *)
                log_error "无效选择，跳过WebDAV配置"
                ENABLE_WEBDAV="n"
                ;;
        esac
        
        if [ "$ENABLE_WEBDAV" = "y" ]; then
            read -p "WebDAV远程目录名 (默认: mysql_backup): " WEBDAV_REMOTE_DIR
            WEBDAV_REMOTE_DIR=${WEBDAV_REMOTE_DIR:-mysql_backup}
        fi
    fi
}

# 验证MySQL连接
verify_mysql_connection() {
    log_step "验证MySQL连接..."
    
    if ! mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "MySQL连接失败，请检查root密码"
        exit 1
    fi
    
    log_info "MySQL连接验证成功"
}

# 检查数据库是否存在
check_database_exists() {
    log_step "检查数据库 $DB_NAME 是否存在..."
    
    local db_exists=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SHOW DATABASES LIKE '$DB_NAME';" | grep -c "$DB_NAME")
    
    if [ $db_exists -eq 0 ]; then
        log_error "数据库 $DB_NAME 不存在"
        exit 1
    fi
    
    log_info "数据库 $DB_NAME 存在"
}

# 启用MySQL Binlog
enable_mysql_binlog() {
    log_step "配置MySQL Binlog..."
    
    local mysql_config="/etc/my.cnf"
    
    # 备份原配置文件
    cp "$mysql_config" "${mysql_config}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 检查是否已启用binlog
    if grep -q "^log-bin" "$mysql_config"; then
        log_info "MySQL Binlog已启用"
    else
        log_info "启用MySQL Binlog..."
        
        # 添加binlog配置
        cat >> "$mysql_config" << EOF

# MySQL实时备份配置
log-bin=mysql-bin
server-id=1
binlog-format=ROW
expire_logs_days=7
max_binlog_size=100M
binlog-do-db=$DB_NAME
EOF
        
        log_info "MySQL配置已更新，重启MySQL服务..."
        
        # 重启MySQL
        /etc/init.d/mysqld restart
        
        if [ $? -eq 0 ]; then
            log_info "MySQL服务重启成功"
        else
            log_error "MySQL服务重启失败"
            exit 1
        fi
    fi
    
    # 验证binlog是否启用
    local binlog_status=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SHOW VARIABLES LIKE 'log_bin';" | grep log_bin | awk '{print $2}')
    
    if [ "$binlog_status" = "ON" ]; then
        log_info "MySQL Binlog启用成功"
    else
        log_error "MySQL Binlog启用失败"
        exit 1
    fi
}

# 创建备份用户
create_backup_user() {
    log_step "创建备份用户 $BACKUP_USER..."
    
    mysql -u root -p"$MYSQL_ROOT_PASS" << EOF
-- 删除已存在的用户（如果存在）
DROP USER IF EXISTS '$BACKUP_USER'@'localhost';

-- 创建备份用户
CREATE USER '$BACKUP_USER'@'localhost' IDENTIFIED BY '$BACKUP_PASS';

-- 授予权限
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON *.* TO '$BACKUP_USER'@'localhost';
GRANT REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO '$BACKUP_USER'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
EOF
    
    if [ $? -eq 0 ]; then
        log_info "备份用户创建成功"
    else
        log_error "备份用户创建失败"
        exit 1
    fi
}

# 创建目录结构
create_directories() {
    log_step "创建备份目录结构..."
    
    mkdir -p "$BACKUP_DIR"/{mysql/{full,incremental,binlog},scripts,logs}
    
    # 设置权限
    chmod 755 "$BACKUP_DIR"
    chmod 700 "$BACKUP_DIR/mysql"
    
    log_info "备份目录创建完成: $BACKUP_DIR"
}

# 部署备份脚本
deploy_scripts() {
    log_step "部署备份脚本..."
    
    local script_dir="$BACKUP_DIR/scripts"
    
    # 复制脚本文件
    cp mysql_realtime_backup.sh "$script_dir/"
    cp mysql_realtime_monitor.sh "$script_dir/"
    
    # 如果启用WebDAV，复制WebDAV脚本
    if [ "$ENABLE_WEBDAV" = "y" ] || [ "$ENABLE_WEBDAV" = "Y" ]; then
        cp webdav_sync.sh "$script_dir/"
        cp webdav_config.conf "$script_dir/"
    fi
    
    # 设置执行权限
    chmod +x "$script_dir"/*.sh
    
    # 更新脚本中的配置
    sed -i "s/DB_USER=\"backup_user\"/DB_USER=\"$BACKUP_USER\"/" "$script_dir/mysql_realtime_backup.sh"
    sed -i "s/DB_PASS=\"请修改为您的密码\"/DB_PASS=\"$BACKUP_PASS\"/" "$script_dir/mysql_realtime_backup.sh"
    sed -i "s/DB_NAME=\"请修改为您的数据库名\"/DB_NAME=\"$DB_NAME\"/" "$script_dir/mysql_realtime_backup.sh"
    sed -i "s|BACKUP_BASE_DIR=\"/www/backup/mysql\"|BACKUP_BASE_DIR=\"$BACKUP_DIR/mysql\"|" "$script_dir/mysql_realtime_backup.sh"
    
    sed -i "s/DB_USER=\"backup_user\"/DB_USER=\"$BACKUP_USER\"/" "$script_dir/mysql_realtime_monitor.sh"
    sed -i "s/DB_PASS=\"请修改为您的密码\"/DB_PASS=\"$BACKUP_PASS\"/" "$script_dir/mysql_realtime_monitor.sh"
    sed -i "s/DB_NAME=\"请修改为您的数据库名\"/DB_NAME=\"$DB_NAME\"/" "$script_dir/mysql_realtime_monitor.sh"
    sed -i "s|BACKUP_SCRIPT=\"/www/backup/scripts/mysql_realtime_backup.sh\"|BACKUP_SCRIPT=\"$script_dir/mysql_realtime_backup.sh\"|" "$script_dir/mysql_realtime_monitor.sh"
    
    # 配置WebDAV
    if [ "$ENABLE_WEBDAV" = "y" ] || [ "$ENABLE_WEBDAV" = "Y" ]; then
        sed -i "s|WEBDAV_URL=\"https://your-webdav-server.com/dav/\"|WEBDAV_URL=\"$WEBDAV_URL\"|" "$script_dir/webdav_sync.sh"
        sed -i "s/WEBDAV_USERNAME=\"your_username\"/WEBDAV_USERNAME=\"$WEBDAV_USERNAME\"/" "$script_dir/webdav_sync.sh"
        sed -i "s/WEBDAV_PASSWORD=\"your_password\"/WEBDAV_PASSWORD=\"$WEBDAV_PASSWORD\"/" "$script_dir/webdav_sync.sh"
        sed -i "s/WEBDAV_REMOTE_DIR=\"mysql_backup\"/WEBDAV_REMOTE_DIR=\"$WEBDAV_REMOTE_DIR\"/" "$script_dir/webdav_sync.sh"
        sed -i "s|LOCAL_BACKUP_DIR=\"/www/backup/mysql\"|LOCAL_BACKUP_DIR=\"$BACKUP_DIR/mysql\"|" "$script_dir/webdav_sync.sh"
    fi
    
    log_info "备份脚本部署完成"
}

# 测试WebDAV连接
test_webdav_connection() {
    if [ "$ENABLE_WEBDAV" = "y" ] || [ "$ENABLE_WEBDAV" = "Y" ]; then
        log_step "测试WebDAV连接..."
        
        local script_dir="$BACKUP_DIR/scripts"
        if "$script_dir/webdav_sync.sh" test; then
            log_info "WebDAV连接测试成功"
        else
            log_error "WebDAV连接测试失败，请检查配置"
            read -p "是否继续部署（不启用WebDAV同步）? (y/n): " CONTINUE_WITHOUT_WEBDAV
            if [ "$CONTINUE_WITHOUT_WEBDAV" != "y" ]; then
                exit 1
            else
                ENABLE_WEBDAV="n"
                log_warn "跳过WebDAV配置"
            fi
        fi
    fi
}

# 创建systemd服务
create_systemd_services() {
    log_step "创建systemd服务..."
    
    # MySQL备份监控服务
    cat > /etc/systemd/system/mysql-realtime-backup.service << EOF
[Unit]
Description=MySQL Real-time Backup Monitor
After=mysqld.service
Requires=mysqld.service

[Service]
Type=simple
User=root
ExecStart=$BACKUP_DIR/scripts/mysql_realtime_monitor.sh start
ExecStop=$BACKUP_DIR/scripts/mysql_realtime_monitor.sh stop
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # WebDAV同步服务
    if [ "$ENABLE_WEBDAV" = "y" ] || [ "$ENABLE_WEBDAV" = "Y" ]; then
        cat > /etc/systemd/system/webdav-sync.service << EOF
[Unit]
Description=WebDAV Real-time Sync Service
After=network.target mysql-realtime-backup.service

[Service]
Type=simple
User=root
ExecStart=$BACKUP_DIR/scripts/webdav_sync.sh start
ExecStop=$BACKUP_DIR/scripts/webdav_sync.sh stop
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_info "systemd服务创建完成"
}

# 配置定时任务
setup_cron_jobs() {
    log_step "配置定时任务..."

    local script_dir="$BACKUP_DIR/scripts"

    # 添加cron任务
    (crontab -l 2>/dev/null; cat << EOF
# MySQL实时备份定时任务
0 2 * * * $script_dir/mysql_realtime_backup.sh full >/dev/null 2>&1
0 */6 * * * $script_dir/mysql_realtime_backup.sh binlog >/dev/null 2>&1
0 3 * * * $script_dir/mysql_realtime_backup.sh cleanup >/dev/null 2>&1
EOF
    ) | crontab -

    log_info "定时任务配置完成"
}

# 初始化备份环境
initialize_backup() {
    log_step "初始化备份环境..."

    local script_dir="$BACKUP_DIR/scripts"

    # 执行初始化
    "$script_dir/mysql_realtime_backup.sh" init

    if [ $? -eq 0 ]; then
        log_info "备份环境初始化成功"
    else
        log_error "备份环境初始化失败"
        exit 1
    fi

    # 执行首次全量备份
    log_info "执行首次全量备份..."
    "$script_dir/mysql_realtime_backup.sh" full

    if [ $? -eq 0 ]; then
        log_info "首次全量备份完成"
    else
        log_error "首次全量备份失败"
        exit 1
    fi
}

# 初始化WebDAV环境
initialize_webdav() {
    if [ "$ENABLE_WEBDAV" = "y" ] || [ "$ENABLE_WEBDAV" = "Y" ]; then
        log_step "初始化WebDAV环境..."

        local script_dir="$BACKUP_DIR/scripts"

        # 初始化WebDAV目录结构
        "$script_dir/webdav_sync.sh" init

        if [ $? -eq 0 ]; then
            log_info "WebDAV环境初始化成功"

            # 执行首次同步
            log_info "执行首次WebDAV同步..."
            "$script_dir/webdav_sync.sh" sync

            if [ $? -eq 0 ]; then
                log_info "首次WebDAV同步完成"
            else
                log_warn "首次WebDAV同步失败，但不影响备份功能"
            fi
        else
            log_error "WebDAV环境初始化失败"
            exit 1
        fi
    fi
}

# 启动服务
start_services() {
    log_step "启动实时备份服务..."

    # 启用并启动MySQL备份服务
    systemctl enable mysql-realtime-backup
    systemctl start mysql-realtime-backup

    if [ $? -eq 0 ]; then
        log_info "MySQL实时备份服务启动成功"
    else
        log_error "MySQL实时备份服务启动失败"
        exit 1
    fi

    # 启用并启动WebDAV同步服务
    if [ "$ENABLE_WEBDAV" = "y" ] || [ "$ENABLE_WEBDAV" = "Y" ]; then
        systemctl enable webdav-sync
        systemctl start webdav-sync

        if [ $? -eq 0 ]; then
            log_info "WebDAV同步服务启动成功"
        else
            log_warn "WebDAV同步服务启动失败，但MySQL备份服务正常运行"
        fi
    fi
}

# 显示部署结果
show_deployment_result() {
    echo ""
    echo "=========================================="
    log_info "MySQL实时备份系统部署完成！"
    echo "=========================================="
    echo ""
    echo "配置信息："
    echo "  数据库名: $DB_NAME"
    echo "  备份用户: $BACKUP_USER"
    echo "  备份目录: $BACKUP_DIR"
    echo "  WebDAV同步: $([ "$ENABLE_WEBDAV" = "y" ] && echo "已启用" || echo "未启用")"
    if [ "$ENABLE_WEBDAV" = "y" ]; then
        echo "  WebDAV服务器: $WEBDAV_URL"
        echo "  WebDAV用户: $WEBDAV_USERNAME"
        echo "  远程目录: $WEBDAV_REMOTE_DIR"
    fi
    echo ""
    echo "服务管理命令："
    echo "  查看MySQL备份服务状态: systemctl status mysql-realtime-backup"
    echo "  启动MySQL备份服务: systemctl start mysql-realtime-backup"
    echo "  停止MySQL备份服务: systemctl stop mysql-realtime-backup"
    echo "  重启MySQL备份服务: systemctl restart mysql-realtime-backup"

    if [ "$ENABLE_WEBDAV" = "y" ]; then
        echo ""
        echo "  查看WebDAV同步服务状态: systemctl status webdav-sync"
        echo "  启动WebDAV同步服务: systemctl start webdav-sync"
        echo "  停止WebDAV同步服务: systemctl stop webdav-sync"
        echo "  重启WebDAV同步服务: systemctl restart webdav-sync"
    fi

    echo ""
    echo "手动备份命令："
    echo "  全量备份: $BACKUP_DIR/scripts/mysql_realtime_backup.sh full"
    echo "  增量备份: $BACKUP_DIR/scripts/mysql_realtime_backup.sh incremental"
    echo "  查看备份状态: $BACKUP_DIR/scripts/mysql_realtime_backup.sh status"

    if [ "$ENABLE_WEBDAV" = "y" ]; then
        echo ""
        echo "WebDAV同步命令："
        echo "  测试WebDAV连接: $BACKUP_DIR/scripts/webdav_sync.sh test"
        echo "  手动同步: $BACKUP_DIR/scripts/webdav_sync.sh sync"
        echo "  查看同步状态: $BACKUP_DIR/scripts/webdav_sync.sh status"
    fi

    echo ""
    echo "日志文件："
    echo "  MySQL备份日志: /var/log/mysql_backup.log"
    echo "  MySQL监控日志: /var/log/mysql_realtime_monitor.log"
    echo "  MySQL错误日志: /var/log/mysql_backup_error.log"

    if [ "$ENABLE_WEBDAV" = "y" ]; then
        echo "  WebDAV同步日志: /var/log/webdav_sync.log"
        echo "  WebDAV错误日志: /var/log/webdav_sync_error.log"
    fi

    echo ""
    echo "查看实时日志："
    echo "  MySQL备份: tail -f /var/log/mysql_backup.log"
    echo "  MySQL监控: tail -f /var/log/mysql_realtime_monitor.log"

    if [ "$ENABLE_WEBDAV" = "y" ]; then
        echo "  WebDAV同步: tail -f /var/log/webdav_sync.log"
    fi

    echo ""
    log_info "部署完成！实时备份系统已开始运行。"

    if [ "$ENABLE_WEBDAV" = "y" ]; then
        echo ""
        log_info "WebDAV同步已启用，备份文件将自动同步到云端。"
        echo "您可以在WebDAV服务器的 $WEBDAV_REMOTE_DIR 目录中查看备份文件。"
    fi
}

# 主函数
main() {
    check_root
    check_bt_panel
    check_mysql
    install_dependencies
    get_user_input
    verify_mysql_connection
    check_database_exists
    enable_mysql_binlog
    create_backup_user
    create_directories
    deploy_scripts
    test_webdav_connection
    create_systemd_services
    setup_cron_jobs
    initialize_backup
    initialize_webdav
    start_services
    show_deployment_result
}

# 执行主函数
main "$@"
