#!/bin/bash
# MySQL实时备份系统部署脚本
# 适用于宝塔面板 + MySQL 5.6.50

echo "=========================================="
echo "MySQL实时备份系统部署脚本"
echo "适用于宝塔面板 + MySQL 5.6.50"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查宝塔面板
check_bt_panel() {
    if [ ! -f "/etc/init.d/bt" ]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    log_info "检测到宝塔面板"
}

# 检查MySQL
check_mysql() {
    if ! command -v mysql &> /dev/null; then
        log_error "未检测到MySQL，请先在宝塔面板中安装MySQL"
        exit 1
    fi
    
    local mysql_version=$(mysql --version | grep -oP '\d+\.\d+\.\d+' | head -1)
    log_info "检测到MySQL版本: $mysql_version"
}

# 获取用户输入
get_user_input() {
    echo ""
    echo "请输入以下配置信息："
    
    # 数据库信息
    read -p "MySQL root密码: " -s MYSQL_ROOT_PASS
    echo ""
    read -p "要备份的数据库名: " DB_NAME
    read -p "备份用户名 (默认: backup_user): " BACKUP_USER
    BACKUP_USER=${BACKUP_USER:-backup_user}
    read -p "备份用户密码: " -s BACKUP_PASS
    echo ""
    
    # 备份目录
    read -p "备份目录 (默认: /www/backup): " BACKUP_DIR
    BACKUP_DIR=${BACKUP_DIR:-/www/backup}
    
    # 远程备份配置
    read -p "是否启用远程备份? (y/n, 默认: n): " ENABLE_REMOTE
    ENABLE_REMOTE=${ENABLE_REMOTE:-n}
    
    if [ "$ENABLE_REMOTE" = "y" ] || [ "$ENABLE_REMOTE" = "Y" ]; then
        read -p "远程服务器地址: " REMOTE_SERVER
        read -p "远程用户名: " REMOTE_USER
        read -p "远程备份目录: " REMOTE_DIR
    fi
}

# 验证MySQL连接
verify_mysql_connection() {
    log_info "验证MySQL连接..."
    
    if ! mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "MySQL连接失败，请检查root密码"
        exit 1
    fi
    
    log_info "MySQL连接验证成功"
}

# 检查数据库是否存在
check_database_exists() {
    log_info "检查数据库 $DB_NAME 是否存在..."
    
    local db_exists=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SHOW DATABASES LIKE '$DB_NAME';" | grep -c "$DB_NAME")
    
    if [ $db_exists -eq 0 ]; then
        log_error "数据库 $DB_NAME 不存在"
        exit 1
    fi
    
    log_info "数据库 $DB_NAME 存在"
}

# 启用MySQL Binlog
enable_mysql_binlog() {
    log_info "配置MySQL Binlog..."
    
    local mysql_config="/etc/my.cnf"
    
    # 备份原配置文件
    cp "$mysql_config" "${mysql_config}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 检查是否已启用binlog
    if grep -q "^log-bin" "$mysql_config"; then
        log_info "MySQL Binlog已启用"
    else
        log_info "启用MySQL Binlog..."
        
        # 添加binlog配置
        cat >> "$mysql_config" << EOF

# MySQL实时备份配置
log-bin=mysql-bin
server-id=1
binlog-format=ROW
expire_logs_days=7
max_binlog_size=100M
binlog-do-db=$DB_NAME
EOF
        
        log_info "MySQL配置已更新，需要重启MySQL服务"
        
        # 重启MySQL
        /etc/init.d/mysqld restart
        
        if [ $? -eq 0 ]; then
            log_info "MySQL服务重启成功"
        else
            log_error "MySQL服务重启失败"
            exit 1
        fi
    fi
    
    # 验证binlog是否启用
    local binlog_status=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SHOW VARIABLES LIKE 'log_bin';" | grep log_bin | awk '{print $2}')
    
    if [ "$binlog_status" = "ON" ]; then
        log_info "MySQL Binlog启用成功"
    else
        log_error "MySQL Binlog启用失败"
        exit 1
    fi
}

# 创建备份用户
create_backup_user() {
    log_info "创建备份用户 $BACKUP_USER..."

    # 先检查用户是否存在，如果存在则删除（兼容MySQL 5.6）
    local user_exists=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT COUNT(*) FROM mysql.user WHERE User='$BACKUP_USER' AND Host='localhost';" 2>/dev/null | tail -1)

    if [ "$user_exists" = "1" ]; then
        log_info "删除已存在的备份用户..."
        mysql -u root -p"$MYSQL_ROOT_PASS" -e "DROP USER '$BACKUP_USER'@'localhost';" 2>/dev/null
    fi

    # 创建新用户
    mysql -u root -p"$MYSQL_ROOT_PASS" << EOF
-- 创建备份用户
CREATE USER '$BACKUP_USER'@'localhost' IDENTIFIED BY '$BACKUP_PASS';

-- 授予权限
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON *.* TO '$BACKUP_USER'@'localhost';
GRANT REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO '$BACKUP_USER'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
EOF

    if [ $? -eq 0 ]; then
        log_info "备份用户创建成功"
    else
        log_error "备份用户创建失败"
        exit 1
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建备份目录结构..."
    
    mkdir -p "$BACKUP_DIR"/{mysql/{full,incremental,binlog},scripts,logs}
    
    # 设置权限
    chmod 755 "$BACKUP_DIR"
    chmod 700 "$BACKUP_DIR/mysql"
    
    log_info "备份目录创建完成: $BACKUP_DIR"
}

# 部署备份脚本
deploy_scripts() {
    log_info "部署备份脚本..."
    
    local script_dir="$BACKUP_DIR/scripts"
    
    # 复制脚本文件
    cp mysql_realtime_backup.sh "$script_dir/"
    cp mysql_realtime_monitor.sh "$script_dir/"
    
    # 设置执行权限
    chmod +x "$script_dir"/*.sh
    
    # 更新脚本中的配置
    sed -i "s/DB_USER=\"backup_user\"/DB_USER=\"$BACKUP_USER\"/" "$script_dir/mysql_realtime_backup.sh"
    sed -i "s/DB_PASS=\"请修改为您的密码\"/DB_PASS=\"$BACKUP_PASS\"/" "$script_dir/mysql_realtime_backup.sh"
    sed -i "s/DB_NAME=\"请修改为您的数据库名\"/DB_NAME=\"$DB_NAME\"/" "$script_dir/mysql_realtime_backup.sh"
    sed -i "s|BACKUP_BASE_DIR=\"/www/backup/mysql\"|BACKUP_BASE_DIR=\"$BACKUP_DIR/mysql\"|" "$script_dir/mysql_realtime_backup.sh"
    
    sed -i "s/DB_USER=\"backup_user\"/DB_USER=\"$BACKUP_USER\"/" "$script_dir/mysql_realtime_monitor.sh"
    sed -i "s/DB_PASS=\"请修改为您的密码\"/DB_PASS=\"$BACKUP_PASS\"/" "$script_dir/mysql_realtime_monitor.sh"
    sed -i "s/DB_NAME=\"请修改为您的数据库名\"/DB_NAME=\"$DB_NAME\"/" "$script_dir/mysql_realtime_monitor.sh"
    sed -i "s|BACKUP_SCRIPT=\"/www/backup/scripts/mysql_realtime_backup.sh\"|BACKUP_SCRIPT=\"$script_dir/mysql_realtime_backup.sh\"|" "$script_dir/mysql_realtime_monitor.sh"
    
    # 配置远程备份
    if [ "$ENABLE_REMOTE" = "y" ] || [ "$ENABLE_REMOTE" = "Y" ]; then
        sed -i "s/ENABLE_REMOTE_BACKUP=false/ENABLE_REMOTE_BACKUP=true/" "$script_dir/mysql_realtime_backup.sh"
        sed -i "s/REMOTE_SERVER=\"\"/REMOTE_SERVER=\"$REMOTE_SERVER\"/" "$script_dir/mysql_realtime_backup.sh"
        sed -i "s/REMOTE_USER=\"\"/REMOTE_USER=\"$REMOTE_USER\"/" "$script_dir/mysql_realtime_backup.sh"
        sed -i "s/REMOTE_DIR=\"\"/REMOTE_DIR=\"$REMOTE_DIR\"/" "$script_dir/mysql_realtime_backup.sh"
    fi
    
    log_info "备份脚本部署完成"
}

# 创建systemd服务
create_systemd_service() {
    log_info "创建systemd服务..."
    
    cat > /etc/systemd/system/mysql-realtime-backup.service << EOF
[Unit]
Description=MySQL Real-time Backup Monitor
After=mysqld.service
Requires=mysqld.service

[Service]
Type=simple
User=root
ExecStart=$BACKUP_DIR/scripts/mysql_realtime_monitor.sh start
ExecStop=$BACKUP_DIR/scripts/mysql_realtime_monitor.sh stop
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_info "systemd服务创建完成"
}

# 配置定时任务
setup_cron_jobs() {
    log_info "配置定时任务..."
    
    local script_dir="$BACKUP_DIR/scripts"
    
    # 添加cron任务
    (crontab -l 2>/dev/null; cat << EOF
# MySQL实时备份定时任务
0 2 * * * $script_dir/mysql_realtime_backup.sh full >/dev/null 2>&1
0 */6 * * * $script_dir/mysql_realtime_backup.sh binlog >/dev/null 2>&1
0 3 * * * $script_dir/mysql_realtime_backup.sh cleanup >/dev/null 2>&1
EOF
    ) | crontab -
    
    log_info "定时任务配置完成"
}

# 初始化备份环境
initialize_backup() {
    log_info "初始化备份环境..."
    
    local script_dir="$BACKUP_DIR/scripts"
    
    # 执行初始化
    "$script_dir/mysql_realtime_backup.sh" init
    
    if [ $? -eq 0 ]; then
        log_info "备份环境初始化成功"
    else
        log_error "备份环境初始化失败"
        exit 1
    fi
    
    # 执行首次全量备份
    log_info "执行首次全量备份..."
    "$script_dir/mysql_realtime_backup.sh" full
    
    if [ $? -eq 0 ]; then
        log_info "首次全量备份完成"
    else
        log_error "首次全量备份失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动实时备份服务..."
    
    # 启用并启动systemd服务
    systemctl enable mysql-realtime-backup
    systemctl start mysql-realtime-backup
    
    if [ $? -eq 0 ]; then
        log_info "实时备份服务启动成功"
    else
        log_error "实时备份服务启动失败"
        exit 1
    fi
}

# 显示部署结果
show_deployment_result() {
    echo ""
    echo "=========================================="
    log_info "MySQL实时备份系统部署完成！"
    echo "=========================================="
    echo ""
    echo "配置信息："
    echo "  数据库名: $DB_NAME"
    echo "  备份用户: $BACKUP_USER"
    echo "  备份目录: $BACKUP_DIR"
    echo "  远程备份: $([ "$ENABLE_REMOTE" = "y" ] && echo "已启用" || echo "未启用")"
    echo ""
    echo "管理命令："
    echo "  查看服务状态: systemctl status mysql-realtime-backup"
    echo "  启动服务: systemctl start mysql-realtime-backup"
    echo "  停止服务: systemctl stop mysql-realtime-backup"
    echo "  重启服务: systemctl restart mysql-realtime-backup"
    echo ""
    echo "手动备份命令："
    echo "  全量备份: $BACKUP_DIR/scripts/mysql_realtime_backup.sh full"
    echo "  增量备份: $BACKUP_DIR/scripts/mysql_realtime_backup.sh incremental"
    echo "  查看状态: $BACKUP_DIR/scripts/mysql_realtime_backup.sh status"
    echo ""
    echo "日志文件："
    echo "  备份日志: /var/log/mysql_backup.log"
    echo "  监控日志: /var/log/mysql_realtime_monitor.log"
    echo "  错误日志: /var/log/mysql_backup_error.log"
    echo ""
    log_info "部署完成！实时备份系统已开始运行。"
}

# 主函数
main() {
    check_root
    check_bt_panel
    check_mysql
    get_user_input
    verify_mysql_connection
    check_database_exists
    enable_mysql_binlog
    create_backup_user
    create_directories
    deploy_scripts
    create_systemd_service
    setup_cron_jobs
    initialize_backup
    start_services
    show_deployment_result
}

# 执行主函数
main "$@"
