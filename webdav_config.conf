# WebDAV配置文件
# 请根据您的WebDAV服务器信息修改以下配置

# ===================WebDAV服务器配置===================

# 服务器地址（必填）
# 示例：
# - 坚果云: https://dav.jianguoyun.com/dav/
# - OneDrive: https://your-tenant.sharepoint.com/sites/your-site/_vti_bin/
# - Nextcloud: https://your-domain.com/nextcloud/remote.php/dav/files/username/
# - ownCloud: https://your-domain.com/owncloud/remote.php/webdav/
WEBDAV_URL="https://dav.jianguoyun.com/dav/"

# 用户名（必填）
WEBDAV_USERNAME="<EMAIL>"

# 密码或应用密码（必填）
# 注意：某些服务需要使用应用专用密码而不是登录密码
WEBDAV_PASSWORD="your_app_password"

# 远程备份目录名（可选，默认：mysql_backup）
WEBDAV_REMOTE_DIR="mysql_backup"

# ===================同步配置===================

# 本地备份目录（通常不需要修改）
LOCAL_BACKUP_DIR="/www/backup/mysql"

# 同步间隔（秒，默认：60）
SYNC_INTERVAL=60

# 最大重试次数（默认：3）
MAX_RETRY_COUNT=3

# 重试延迟（秒，默认：10）
RETRY_DELAY=10

# 带宽限制（KB/s，0表示不限制）
BANDWIDTH_LIMIT=0

# ===================日志配置===================

# 日志文件路径
LOG_FILE="/var/log/webdav_sync.log"
ERROR_LOG="/var/log/webdav_sync_error.log"

# PID文件路径
PID_FILE="/var/run/webdav_sync.pid"

# ===================常见WebDAV服务器配置示例===================

# 1. 坚果云配置示例
# WEBDAV_URL="https://dav.jianguoyun.com/dav/"
# WEBDAV_USERNAME="<EMAIL>"
# WEBDAV_PASSWORD="your_app_password"  # 在坚果云设置中生成应用密码

# 2. OneDrive配置示例（需要SharePoint）
# WEBDAV_URL="https://your-tenant.sharepoint.com/sites/your-site/_vti_bin/"
# WEBDAV_USERNAME="<EMAIL>"
# WEBDAV_PASSWORD="your_password"

# 3. Nextcloud配置示例
# WEBDAV_URL="https://your-domain.com/nextcloud/remote.php/dav/files/username/"
# WEBDAV_USERNAME="your_username"
# WEBDAV_PASSWORD="your_password"

# 4. ownCloud配置示例
# WEBDAV_URL="https://your-domain.com/owncloud/remote.php/webdav/"
# WEBDAV_USERNAME="your_username"
# WEBDAV_PASSWORD="your_password"

# 5. 群晖NAS WebDAV配置示例
# WEBDAV_URL="https://your-nas-ip:5006/"
# WEBDAV_USERNAME="your_nas_username"
# WEBDAV_PASSWORD="your_nas_password"

# 6. 自建WebDAV服务器配置示例
# WEBDAV_URL="https://your-server.com/webdav/"
# WEBDAV_USERNAME="your_username"
# WEBDAV_PASSWORD="your_password"

# ===================配置说明===================

# 1. 获取WebDAV信息：
#    - 坚果云：账户信息 -> 安全选项 -> 第三方应用管理 -> 添加应用密码
#    - OneDrive：需要SharePoint或OneDrive for Business
#    - Nextcloud/ownCloud：设置 -> 安全 -> 应用密码
#    - 群晖NAS：控制面板 -> 文件服务 -> WebDAV

# 2. 测试连接：
#    运行 ./webdav_sync.sh test 来测试WebDAV连接

# 3. 安全建议：
#    - 使用应用专用密码而不是主密码
#    - 定期更换密码
#    - 限制WebDAV访问权限

# 4. 网络配置：
#    - 确保服务器可以访问WebDAV服务器
#    - 如果有防火墙，请开放相应端口
#    - 考虑使用HTTPS确保传输安全

# ===================故障排除===================

# 常见问题及解决方案：

# 1. 连接超时
#    - 检查网络连接
#    - 确认WebDAV服务器地址正确
#    - 检查防火墙设置

# 2. 认证失败
#    - 确认用户名密码正确
#    - 检查是否需要使用应用密码
#    - 确认账户未被锁定

# 3. 权限错误
#    - 确认用户有写入权限
#    - 检查目录是否存在
#    - 确认磁盘空间充足

# 4. 上传失败
#    - 检查文件大小限制
#    - 确认网络稳定性
#    - 查看错误日志获取详细信息
