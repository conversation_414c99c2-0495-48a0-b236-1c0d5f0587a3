#!/bin/bash
# 完整卸载MySQL备份系统

echo "=========================================="
echo "完整卸载MySQL备份系统"
echo "=========================================="

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 确认卸载
echo "此操作将完全卸载MySQL备份系统，包括："
echo "- 停止并删除所有相关服务"
echo "- 清理定时任务"
echo "- 删除脚本文件"
echo "- 清理日志文件"
echo ""
echo "备份文件将保留在 /www/backup 目录中"
echo ""
read -p "确认要继续卸载吗？(y/N): " confirm

if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "取消卸载"
    exit 0
fi

echo ""
log_info "开始卸载..."

# 1. 停止所有服务
log_info "停止服务..."
systemctl stop mysql-realtime-backup 2>/dev/null
systemctl stop webdav-sync 2>/dev/null

# 2. 禁用服务
log_info "禁用服务..."
systemctl disable mysql-realtime-backup 2>/dev/null
systemctl disable webdav-sync 2>/dev/null

# 3. 删除systemd服务文件
log_info "删除服务文件..."
rm -f /etc/systemd/system/mysql-realtime-backup.service
rm -f /etc/systemd/system/webdav-sync.service

# 4. 重新加载systemd
systemctl daemon-reload

# 5. 备份并清理定时任务
log_info "清理定时任务..."
if crontab -l >/dev/null 2>&1; then
    # 备份当前定时任务
    crontab -l > "/tmp/cron_backup_$(date +%Y%m%d_%H%M%S).txt"
    log_info "定时任务已备份到 /tmp/cron_backup_*.txt"
    
    # 删除备份相关的定时任务
    crontab -l | grep -v "mysql_realtime_backup\|webdav_sync\|upload_single_file" | crontab -
    log_info "备份相关定时任务已清理"
fi

# 6. 停止相关进程
log_info "停止相关进程..."
pkill -f "mysql_realtime_monitor" 2>/dev/null
pkill -f "webdav_sync" 2>/dev/null
pkill -f "mysql_realtime_backup" 2>/dev/null

# 7. 清理进程文件
log_info "清理进程文件..."
rm -f /var/run/mysql_realtime_monitor.pid
rm -f /var/run/webdav_sync.pid
rm -f /tmp/mysql_monitor_*
rm -f /tmp/webdav_*

# 8. 清理日志文件
log_info "清理日志文件..."
rm -f /var/log/mysql_backup*.log
rm -f /var/log/webdav_sync*.log
rm -f /var/log/mysql_realtime_monitor.log

# 9. 删除脚本文件（保留备份目录）
log_info "删除脚本文件..."
if [ -d "/www/backup/scripts" ]; then
    rm -rf /www/backup/scripts
fi

# 10. 可选：删除备份用户
echo ""
read -p "是否删除MySQL备份用户 'backup_user'？(y/N): " delete_user

if [ "$delete_user" = "y" ] || [ "$delete_user" = "Y" ]; then
    read -p "请输入MySQL root密码: " -s mysql_root_pass
    echo ""
    
    mysql -u root -p"$mysql_root_pass" << 'EOF' 2>/dev/null
DROP USER IF EXISTS 'backup_user'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    if [ $? -eq 0 ]; then
        log_info "MySQL备份用户已删除"
    else
        log_warn "删除MySQL备份用户失败，请手动删除"
    fi
fi

# 11. 可选：删除备份文件
echo ""
read -p "是否删除所有备份文件？这将删除 /www/backup 目录 (y/N): " delete_backups

if [ "$delete_backups" = "y" ] || [ "$delete_backups" = "Y" ]; then
    read -p "确认删除所有备份文件？此操作不可恢复！(y/N): " confirm_delete
    
    if [ "$confirm_delete" = "y" ] || [ "$confirm_delete" = "Y" ]; then
        rm -rf /www/backup
        log_info "所有备份文件已删除"
    else
        log_info "保留备份文件"
    fi
else
    log_info "保留备份文件在 /www/backup 目录"
fi

echo ""
echo "=========================================="
log_info "卸载完成！"
echo "=========================================="
echo ""
echo "已完成的操作："
echo "✅ 停止并删除所有服务"
echo "✅ 清理定时任务"
echo "✅ 删除脚本文件"
echo "✅ 清理日志和临时文件"
echo ""
echo "保留的内容："
echo "📁 备份文件: /www/backup/mysql/ (如果选择保留)"
echo "📄 定时任务备份: /tmp/cron_backup_*.txt"
echo ""
log_info "现在可以重新部署新版本了！"
