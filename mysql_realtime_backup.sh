#!/bin/bash
# MySQL 5.6.50 实时备份脚本
# 适用于宝塔面板环境

# ===================配置参数===================
# 数据库连接信息
DB_USER="backup_user"
DB_PASS="请修改为您的密码"
DB_NAME="请修改为您的数据库名"
DB_HOST="localhost"
DB_PORT="3306"

# 备份目录配置
BACKUP_BASE_DIR="/www/backup/mysql"
FULL_BACKUP_DIR="$BACKUP_BASE_DIR/full"
INCREMENTAL_DIR="$BACKUP_BASE_DIR/incremental"
BINLOG_DIR="$BACKUP_BASE_DIR/binlog"

# 日志配置
LOG_FILE="/var/log/mysql_backup.log"
ERROR_LOG="/var/log/mysql_backup_error.log"

# 远程备份配置（可选）
ENABLE_REMOTE_BACKUP=false
REMOTE_SERVER=""
REMOTE_DIR=""
REMOTE_USER=""

# 备份保留策略
FULL_BACKUP_RETENTION_DAYS=30
INCREMENTAL_RETENTION_DAYS=7
BINLOG_RETENTION_DAYS=7

# 压缩配置
ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=6

# ===================函数定义===================

# 日志记录函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo "[$timestamp] [INFO] $message" | tee -a $LOG_FILE
            ;;
        "ERROR")
            echo "[$timestamp] [ERROR] $message" | tee -a $LOG_FILE | tee -a $ERROR_LOG
            ;;
        "WARN")
            echo "[$timestamp] [WARN] $message" | tee -a $LOG_FILE
            ;;
    esac
}

# 检查MySQL连接
check_mysql_connection() {
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SELECT 1;" >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_message "ERROR" "无法连接到MySQL数据库"
        return 1
    fi
    return 0
}

# 创建备份目录
create_backup_dirs() {
    for dir in "$FULL_BACKUP_DIR" "$INCREMENTAL_DIR" "$BINLOG_DIR"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_message "INFO" "创建备份目录: $dir"
        fi
    done
}

# 检查Binlog是否启用
check_binlog_enabled() {
    local binlog_status=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW VARIABLES LIKE 'log_bin';" | grep log_bin | awk '{print $2}')
    
    if [ "$binlog_status" != "ON" ]; then
        log_message "ERROR" "MySQL Binlog未启用，请先启用Binlog"
        return 1
    fi
    
    log_message "INFO" "MySQL Binlog已启用"
    return 0
}

# 全量备份函数
full_backup() {
    log_message "INFO" "开始执行全量备份..."
    
    local backup_file="$FULL_BACKUP_DIR/full_backup_$(date +%Y%m%d_%H%M%S).sql"
    local start_time=$(date +%s)
    
    # 执行mysqldump
    mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --master-data=2 \
        --flush-logs \
        --hex-blob \
        --default-character-set=utf8mb4 \
        $DB_NAME > "$backup_file" 2>>"$ERROR_LOG"
    
    local dump_result=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $dump_result -eq 0 ]; then
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_message "INFO" "全量备份完成: $backup_file (大小: $file_size, 耗时: ${duration}秒)"
        
        # 压缩备份文件
        if [ "$ENABLE_COMPRESSION" = true ]; then
            gzip -$COMPRESSION_LEVEL "$backup_file"
            log_message "INFO" "备份文件已压缩: ${backup_file}.gz"
        fi
        
        # 记录备份位置信息
        local position_file="$FULL_BACKUP_DIR/last_full_backup_position.txt"
        mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" > "$position_file"
        
        return 0
    else
        log_message "ERROR" "全量备份失败，请检查错误日志: $ERROR_LOG"
        return 1
    fi
}

# 增量备份函数（基于binlog）
incremental_backup() {
    log_message "INFO" "开始执行增量备份..."
    
    local position_file="$INCREMENTAL_DIR/last_incremental_position.txt"
    local current_status_file="$INCREMENTAL_DIR/current_status.tmp"
    
    # 获取当前master状态
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" | tail -n 1 > "$current_status_file"
    local current_file=$(cat "$current_status_file" | awk '{print $1}')
    local current_pos=$(cat "$current_status_file" | awk '{print $2}')
    
    # 读取上次备份位置
    if [ -f "$position_file" ]; then
        local last_file=$(cat "$position_file" | cut -d: -f1)
        local last_pos=$(cat "$position_file" | cut -d: -f2)
    else
        # 首次增量备份，从最新的全量备份位置开始
        local full_position_file="$FULL_BACKUP_DIR/last_full_backup_position.txt"
        if [ -f "$full_position_file" ]; then
            local last_file=$(tail -n 1 "$full_position_file" | awk '{print $1}')
            local last_pos=$(tail -n 1 "$full_position_file" | awk '{print $2}')
        else
            log_message "WARN" "未找到基准位置，跳过增量备份"
            return 1
        fi
    fi
    
    # 检查是否有新的变化
    if [ "$last_file:$last_pos" = "$current_file:$current_pos" ]; then
        log_message "INFO" "数据库无变化，跳过增量备份"
        return 0
    fi
    
    local inc_file="$INCREMENTAL_DIR/incremental_$(date +%Y%m%d_%H%M%S).sql"
    local start_time=$(date +%s)
    
    # 导出binlog增量数据
    if [ "$last_file" = "$current_file" ]; then
        # 同一个binlog文件内的增量
        mysqlbinlog --start-position=$last_pos --stop-position=$current_pos \
            /var/lib/mysql/$last_file > "$inc_file" 2>>"$ERROR_LOG"
    else
        # 跨binlog文件的增量
        mysqlbinlog --start-position=$last_pos /var/lib/mysql/$last_file > "$inc_file" 2>>"$ERROR_LOG"
        
        # 添加新binlog文件的内容
        local binlog_list=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW BINARY LOGS;" | awk '{print $1}' | grep -v Log_name)
        local start_adding=false
        
        for binlog in $binlog_list; do
            if [ "$start_adding" = true ] && [ "$binlog" != "$current_file" ]; then
                mysqlbinlog /var/lib/mysql/$binlog >> "$inc_file" 2>>"$ERROR_LOG"
            elif [ "$binlog" = "$last_file" ]; then
                start_adding=true
            elif [ "$binlog" = "$current_file" ]; then
                mysqlbinlog --stop-position=$current_pos /var/lib/mysql/$binlog >> "$inc_file" 2>>"$ERROR_LOG"
                break
            fi
        done
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $? -eq 0 ] && [ -s "$inc_file" ]; then
        local file_size=$(du -h "$inc_file" | cut -f1)
        log_message "INFO" "增量备份完成: $inc_file (大小: $file_size, 耗时: ${duration}秒)"
        
        # 压缩备份文件
        if [ "$ENABLE_COMPRESSION" = true ]; then
            gzip -$COMPRESSION_LEVEL "$inc_file"
            log_message "INFO" "增量备份文件已压缩: ${inc_file}.gz"
        fi
        
        # 更新位置记录
        echo "$current_file:$current_pos" > "$position_file"
        
        return 0
    else
        log_message "ERROR" "增量备份失败或无数据变化"
        rm -f "$inc_file"
        return 1
    fi
}

# Binlog文件备份
backup_binlog_files() {
    log_message "INFO" "开始备份Binlog文件..."
    
    local mysql_data_dir="/var/lib/mysql"
    local binlog_list=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW BINARY LOGS;" | awk '{print $1}' | grep -v Log_name)
    
    for binlog in $binlog_list; do
        local source_file="$mysql_data_dir/$binlog"
        local dest_file="$BINLOG_DIR/$binlog"
        
        if [ -f "$source_file" ] && [ ! -f "$dest_file" ]; then
            cp "$source_file" "$dest_file"
            log_message "INFO" "备份Binlog文件: $binlog"
        fi
    done
}

# 清理旧备份
cleanup_old_backups() {
    log_message "INFO" "开始清理旧备份文件..."
    
    # 清理旧的全量备份
    find "$FULL_BACKUP_DIR" -name "full_backup_*.sql*" -mtime +$FULL_BACKUP_RETENTION_DAYS -delete
    
    # 清理旧的增量备份
    find "$INCREMENTAL_DIR" -name "incremental_*.sql*" -mtime +$INCREMENTAL_RETENTION_DAYS -delete
    
    # 清理旧的binlog备份
    find "$BINLOG_DIR" -name "mysql-bin.*" -mtime +$BINLOG_RETENTION_DAYS -delete
    
    log_message "INFO" "旧备份文件清理完成"
}

# 远程同步
sync_to_remote() {
    if [ "$ENABLE_REMOTE_BACKUP" = true ] && [ ! -z "$REMOTE_SERVER" ]; then
        log_message "INFO" "开始同步到远程服务器..."
        
        rsync -avz --delete "$BACKUP_BASE_DIR/" "$REMOTE_USER@$REMOTE_SERVER:$REMOTE_DIR/" 2>>"$ERROR_LOG"
        
        if [ $? -eq 0 ]; then
            log_message "INFO" "远程同步完成"
        else
            log_message "ERROR" "远程同步失败"
        fi
    fi
}

# 备份状态检查
check_backup_status() {
    log_message "INFO" "检查备份状态..."
    
    # 检查最近的全量备份
    local latest_full=$(find "$FULL_BACKUP_DIR" -name "full_backup_*.sql*" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    if [ ! -z "$latest_full" ]; then
        local full_age=$(( ($(date +%s) - $(stat -c %Y "$latest_full")) / 86400 ))
        log_message "INFO" "最新全量备份: $latest_full (${full_age}天前)"
    else
        log_message "WARN" "未找到全量备份文件"
    fi
    
    # 检查最近的增量备份
    local latest_inc=$(find "$INCREMENTAL_DIR" -name "incremental_*.sql*" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    if [ ! -z "$latest_inc" ]; then
        local inc_age=$(( ($(date +%s) - $(stat -c %Y "$latest_inc")) / 3600 ))
        log_message "INFO" "最新增量备份: $latest_inc (${inc_age}小时前)"
    else
        log_message "WARN" "未找到增量备份文件"
    fi
}

# ===================主程序===================

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 {full|incremental|binlog|cleanup|sync|status|init}"
    echo ""
    echo "命令说明:"
    echo "  full        - 执行全量备份"
    echo "  incremental - 执行增量备份"
    echo "  binlog      - 备份binlog文件"
    echo "  cleanup     - 清理旧备份"
    echo "  sync        - 同步到远程服务器"
    echo "  status      - 检查备份状态"
    echo "  init        - 初始化备份环境"
    exit 1
fi

# 初始化
if [ "$1" = "init" ]; then
    log_message "INFO" "初始化备份环境..."
    create_backup_dirs
    
    if ! check_mysql_connection; then
        exit 1
    fi
    
    if ! check_binlog_enabled; then
        exit 1
    fi
    
    log_message "INFO" "备份环境初始化完成"
    exit 0
fi

# 执行前检查
create_backup_dirs

if ! check_mysql_connection; then
    exit 1
fi

# 根据参数执行相应操作
case "$1" in
    "full")
        full_backup
        ;;
    "incremental")
        incremental_backup
        ;;
    "binlog")
        backup_binlog_files
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    "sync")
        sync_to_remote
        ;;
    "status")
        check_backup_status
        ;;
    *)
        echo "未知命令: $1"
        exit 1
        ;;
esac

exit $?
