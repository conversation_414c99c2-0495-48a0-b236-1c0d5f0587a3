#!/bin/bash
# MySQL实时监控脚本
# 监控数据库变化并触发增量备份

# ===================配置参数===================
# 数据库连接信息
DB_USER="backup_user"
DB_PASS="请修改为您的密码"
DB_NAME="请修改为您的数据库名"
DB_HOST="localhost"
DB_PORT="3306"

# 监控配置
MONITOR_INTERVAL=30  # 监控间隔（秒）
BACKUP_SCRIPT="/www/backup/scripts/mysql_realtime_backup.sh"
LOG_FILE="/var/log/mysql_realtime_monitor.log"
PID_FILE="/var/run/mysql_realtime_monitor.pid"

# Binlog监控配置
MYSQL_DATA_DIR="/var/lib/mysql"
BINLOG_INDEX_FILE="$MYSQL_DATA_DIR/mysql-bin.index"

# 变化阈值配置
MIN_CHANGE_SIZE=1024  # 最小变化大小（字节）
MAX_BACKUP_FREQUENCY=300  # 最大备份频率（秒）

# ===================函数定义===================

# 日志记录函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a $LOG_FILE
}

# 检查MySQL连接
check_mysql_connection() {
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SELECT 1;" >/dev/null 2>&1
    return $?
}

# 获取当前binlog位置
get_current_position() {
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" 2>/dev/null | tail -n 1
}

# 检查binlog变化
check_binlog_changes() {
    local current_position=$(get_current_position)
    local current_file=$(echo "$current_position" | awk '{print $1}')
    local current_pos=$(echo "$current_position" | awk '{print $2}')
    
    # 读取上次记录的位置
    local last_position_file="/tmp/mysql_monitor_last_position"
    if [ -f "$last_position_file" ]; then
        local last_position=$(cat "$last_position_file")
        local last_file=$(echo "$last_position" | cut -d: -f1)
        local last_pos=$(echo "$last_position" | cut -d: -f2)
    else
        # 首次运行，记录当前位置
        echo "$current_file:$current_pos" > "$last_position_file"
        return 1
    fi
    
    # 比较位置变化
    if [ "$last_file:$last_pos" != "$current_file:$current_pos" ]; then
        # 计算变化大小
        local change_size=0
        if [ "$last_file" = "$current_file" ]; then
            change_size=$((current_pos - last_pos))
        else
            # 跨文件变化，认为是大变化
            change_size=$((MIN_CHANGE_SIZE + 1))
        fi
        
        if [ $change_size -ge $MIN_CHANGE_SIZE ]; then
            echo "$current_file:$current_pos" > "$last_position_file"
            return 0  # 有显著变化
        fi
    fi
    
    return 1  # 无显著变化
}

# 检查表变化（基于information_schema）
check_table_changes() {
    local current_checksum=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "
        SELECT CONCAT(TABLE_NAME, ':', UPDATE_TIME, ':', TABLE_ROWS) 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA='$DB_NAME' 
        ORDER BY TABLE_NAME;" 2>/dev/null | md5sum | cut -d' ' -f1)
    
    local last_checksum_file="/tmp/mysql_monitor_table_checksum"
    if [ -f "$last_checksum_file" ]; then
        local last_checksum=$(cat "$last_checksum_file")
        if [ "$last_checksum" != "$current_checksum" ]; then
            echo "$current_checksum" > "$last_checksum_file"
            return 0  # 表有变化
        fi
    else
        echo "$current_checksum" > "$last_checksum_file"
        return 1
    fi
    
    return 1  # 表无变化
}

# 检查备份频率限制
check_backup_frequency() {
    local last_backup_file="/tmp/mysql_monitor_last_backup_time"
    local current_time=$(date +%s)
    
    if [ -f "$last_backup_file" ]; then
        local last_backup_time=$(cat "$last_backup_file")
        local time_diff=$((current_time - last_backup_time))
        
        if [ $time_diff -lt $MAX_BACKUP_FREQUENCY ]; then
            return 1  # 备份太频繁
        fi
    fi
    
    echo "$current_time" > "$last_backup_file"
    return 0  # 可以备份
}

# 执行增量备份
trigger_incremental_backup() {
    log_message "INFO" "触发增量备份..."
    
    if [ -x "$BACKUP_SCRIPT" ]; then
        $BACKUP_SCRIPT incremental
        local result=$?
        
        if [ $result -eq 0 ]; then
            log_message "INFO" "增量备份完成"
        else
            log_message "ERROR" "增量备份失败，返回码: $result"
        fi
        
        return $result
    else
        log_message "ERROR" "备份脚本不存在或不可执行: $BACKUP_SCRIPT"
        return 1
    fi
}

# 监控数据库连接状态
monitor_connection_status() {
    local connection_lost_file="/tmp/mysql_monitor_connection_lost"
    
    if check_mysql_connection; then
        # 连接正常
        if [ -f "$connection_lost_file" ]; then
            log_message "INFO" "MySQL连接已恢复"
            rm -f "$connection_lost_file"
        fi
        return 0
    else
        # 连接丢失
        if [ ! -f "$connection_lost_file" ]; then
            log_message "ERROR" "MySQL连接丢失"
            touch "$connection_lost_file"
        fi
        return 1
    fi
}

# 监控磁盘空间
monitor_disk_space() {
    local backup_dir="/www/backup"
    local disk_usage=$(df "$backup_dir" | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ $disk_usage -gt 90 ]; then
        log_message "WARN" "备份目录磁盘使用率过高: ${disk_usage}%"
        # 触发清理
        $BACKUP_SCRIPT cleanup
    elif [ $disk_usage -gt 80 ]; then
        log_message "WARN" "备份目录磁盘使用率较高: ${disk_usage}%"
    fi
}

# 创建PID文件
create_pid_file() {
    if [ -f "$PID_FILE" ]; then
        local old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            log_message "ERROR" "监控进程已在运行 (PID: $old_pid)"
            exit 1
        else
            log_message "WARN" "发现僵尸PID文件，清理中..."
            rm -f "$PID_FILE"
        fi
    fi
    
    echo $$ > "$PID_FILE"
}

# 清理PID文件
cleanup_pid_file() {
    rm -f "$PID_FILE"
}

# 信号处理
handle_signal() {
    log_message "INFO" "收到停止信号，正在退出..."
    cleanup_pid_file
    exit 0
}

# 主监控循环
main_monitor_loop() {
    log_message "INFO" "MySQL实时监控启动 (PID: $$, 监控间隔: ${MONITOR_INTERVAL}秒)"
    
    local check_count=0
    local backup_count=0
    
    while true; do
        check_count=$((check_count + 1))
        
        # 监控连接状态
        if ! monitor_connection_status; then
            sleep $MONITOR_INTERVAL
            continue
        fi
        
        # 每10次检查监控一次磁盘空间
        if [ $((check_count % 10)) -eq 0 ]; then
            monitor_disk_space
        fi
        
        # 检查数据库变化
        local has_changes=false
        
        # 方法1: 检查binlog变化
        if check_binlog_changes; then
            log_message "INFO" "检测到Binlog变化"
            has_changes=true
        fi
        
        # 方法2: 检查表变化（作为补充）
        if check_table_changes; then
            log_message "INFO" "检测到表结构或数据变化"
            has_changes=true
        fi
        
        # 如果有变化且满足备份频率限制，执行备份
        if [ "$has_changes" = true ]; then
            if check_backup_frequency; then
                trigger_incremental_backup
                backup_count=$((backup_count + 1))
                log_message "INFO" "已执行 $backup_count 次增量备份"
            else
                log_message "INFO" "检测到变化但备份频率受限，跳过本次备份"
            fi
        fi
        
        # 每小时输出一次状态信息
        if [ $((check_count % 120)) -eq 0 ]; then
            log_message "INFO" "监控状态: 检查次数=$check_count, 备份次数=$backup_count"
        fi
        
        sleep $MONITOR_INTERVAL
    done
}

# ===================主程序===================

# 设置信号处理
trap handle_signal SIGTERM SIGINT

# 检查参数
case "${1:-start}" in
    "start")
        create_pid_file
        main_monitor_loop
        ;;
    "stop")
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                log_message "INFO" "停止监控进程 (PID: $pid)"
                kill "$pid"
                sleep 2
                if kill -0 "$pid" 2>/dev/null; then
                    log_message "WARN" "强制停止监控进程"
                    kill -9 "$pid"
                fi
                cleanup_pid_file
                log_message "INFO" "监控进程已停止"
            else
                log_message "WARN" "监控进程未运行"
                cleanup_pid_file
            fi
        else
            log_message "WARN" "未找到PID文件，监控进程可能未运行"
        fi
        ;;
    "status")
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                echo "监控进程正在运行 (PID: $pid)"
                exit 0
            else
                echo "监控进程未运行（发现僵尸PID文件）"
                cleanup_pid_file
                exit 1
            fi
        else
            echo "监控进程未运行"
            exit 1
        fi
        ;;
    "restart")
        $0 stop
        sleep 2
        $0 start
        ;;
    *)
        echo "用法: $0 {start|stop|status|restart}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动实时监控"
        echo "  stop    - 停止实时监控"
        echo "  status  - 查看监控状态"
        echo "  restart - 重启监控服务"
        exit 1
        ;;
esac
