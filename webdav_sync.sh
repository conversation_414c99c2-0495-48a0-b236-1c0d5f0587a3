#!/bin/bash
# WebDAV实时同步脚本
# 将本地备份文件实时同步到WebDAV服务器

# ===================配置参数===================
# WebDAV服务器配置
WEBDAV_URL="https://your-webdav-server.com/dav/"
WEBDAV_USERNAME="your_username"
WEBDAV_PASSWORD="your_password"
WEBDAV_REMOTE_DIR="mysql_backup"

# 本地备份目录
LOCAL_BACKUP_DIR="/www/backup/mysql"

# 同步配置
SYNC_INTERVAL=60  # 同步间隔（秒）
MAX_RETRY_COUNT=3  # 最大重试次数
RETRY_DELAY=10     # 重试延迟（秒）

# 日志配置
LOG_FILE="/var/log/webdav_sync.log"
ERROR_LOG="/var/log/webdav_sync_error.log"
PID_FILE="/var/run/webdav_sync.pid"

# 压缩配置
ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=6

# 带宽限制（KB/s，0表示不限制）
BANDWIDTH_LIMIT=0

# ===================函数定义===================

# 日志记录函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo "[$timestamp] [INFO] $message" | tee -a $LOG_FILE
            ;;
        "ERROR")
            echo "[$timestamp] [ERROR] $message" | tee -a $LOG_FILE | tee -a $ERROR_LOG
            ;;
        "WARN")
            echo "[$timestamp] [WARN] $message" | tee -a $LOG_FILE
            ;;
        "DEBUG")
            echo "[$timestamp] [DEBUG] $message" >> $LOG_FILE
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    # 检查inotify-tools
    if ! command -v inotifywait &> /dev/null; then
        missing_deps+=("inotify-tools")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_message "ERROR" "缺少依赖: ${missing_deps[*]}"
        log_message "INFO" "请安装依赖: yum install -y ${missing_deps[*]}"
        return 1
    fi
    
    return 0
}

# 测试WebDAV连接
test_webdav_connection() {
    log_message "INFO" "测试WebDAV连接..."
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null \
        --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
        --request PROPFIND \
        --header "Depth: 0" \
        "$WEBDAV_URL" 2>/dev/null)
    
    if [ "$response" = "207" ] || [ "$response" = "200" ]; then
        log_message "INFO" "WebDAV连接测试成功"
        return 0
    else
        log_message "ERROR" "WebDAV连接测试失败，HTTP状态码: $response"
        return 1
    fi
}

# 创建WebDAV远程目录
create_webdav_directory() {
    local remote_path="$1"
    
    log_message "DEBUG" "创建WebDAV目录: $remote_path"
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null \
        --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
        --request MKCOL \
        "$WEBDAV_URL$remote_path" 2>/dev/null)
    
    if [ "$response" = "201" ] || [ "$response" = "405" ]; then
        # 201: 创建成功, 405: 目录已存在
        return 0
    else
        log_message "WARN" "创建WebDAV目录失败: $remote_path (HTTP: $response)"
        return 1
    fi
}

# 上传文件到WebDAV
upload_to_webdav() {
    local local_file="$1"
    local remote_path="$2"
    local retry_count=0
    
    if [ ! -f "$local_file" ]; then
        log_message "ERROR" "本地文件不存在: $local_file"
        return 1
    fi
    
    # 创建远程目录
    local remote_dir=$(dirname "$remote_path")
    if [ "$remote_dir" != "." ]; then
        create_webdav_directory "$remote_dir"
    fi
    
    while [ $retry_count -lt $MAX_RETRY_COUNT ]; do
        log_message "DEBUG" "上传文件: $local_file -> $remote_path (尝试 $((retry_count + 1)))"
        
        local curl_opts=(
            --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD"
            --upload-file "$local_file"
            --silent
            --show-error
            --write-out "%{http_code}"
            --output /dev/null
        )
        
        # 添加带宽限制
        if [ $BANDWIDTH_LIMIT -gt 0 ]; then
            curl_opts+=(--limit-rate "${BANDWIDTH_LIMIT}k")
        fi
        
        local response=$(curl "${curl_opts[@]}" "$WEBDAV_URL$remote_path" 2>>"$ERROR_LOG")
        
        if [ "$response" = "201" ] || [ "$response" = "204" ]; then
            local file_size=$(du -h "$local_file" | cut -f1)
            log_message "INFO" "文件上传成功: $local_file ($file_size)"
            return 0
        else
            retry_count=$((retry_count + 1))
            log_message "WARN" "文件上传失败: $local_file (HTTP: $response, 重试 $retry_count/$MAX_RETRY_COUNT)"
            
            if [ $retry_count -lt $MAX_RETRY_COUNT ]; then
                sleep $RETRY_DELAY
            fi
        fi
    done
    
    log_message "ERROR" "文件上传最终失败: $local_file"
    return 1
}

# 删除WebDAV文件
delete_from_webdav() {
    local remote_path="$1"
    
    log_message "DEBUG" "删除WebDAV文件: $remote_path"
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null \
        --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
        --request DELETE \
        "$WEBDAV_URL$remote_path" 2>/dev/null)
    
    if [ "$response" = "204" ] || [ "$response" = "404" ]; then
        # 204: 删除成功, 404: 文件不存在
        log_message "INFO" "WebDAV文件删除成功: $remote_path"
        return 0
    else
        log_message "WARN" "WebDAV文件删除失败: $remote_path (HTTP: $response)"
        return 1
    fi
}

# 获取本地文件的相对路径
get_relative_path() {
    local full_path="$1"
    local base_path="$2"
    
    # 移除基础路径前缀
    echo "${full_path#$base_path/}"
}

# 同步单个文件
sync_file() {
    local local_file="$1"
    local action="$2"  # create, modify, delete
    
    local relative_path=$(get_relative_path "$local_file" "$LOCAL_BACKUP_DIR")
    local remote_path="$WEBDAV_REMOTE_DIR/$relative_path"
    
    case "$action" in
        "create"|"modify")
            upload_to_webdav "$local_file" "$remote_path"
            ;;
        "delete")
            delete_from_webdav "$remote_path"
            ;;
        *)
            log_message "ERROR" "未知的同步动作: $action"
            return 1
            ;;
    esac
}

# 全量同步
full_sync() {
    log_message "INFO" "开始全量同步..."
    
    local sync_count=0
    local error_count=0
    
    # 创建远程根目录
    create_webdav_directory "$WEBDAV_REMOTE_DIR"
    
    # 遍历本地文件
    find "$LOCAL_BACKUP_DIR" -type f | while read -r local_file; do
        if sync_file "$local_file" "create"; then
            sync_count=$((sync_count + 1))
        else
            error_count=$((error_count + 1))
        fi
        
        # 每100个文件输出一次进度
        if [ $((sync_count % 100)) -eq 0 ]; then
            log_message "INFO" "已同步 $sync_count 个文件..."
        fi
    done
    
    log_message "INFO" "全量同步完成: 成功 $sync_count 个，失败 $error_count 个"
}

# 实时监控文件变化
monitor_file_changes() {
    log_message "INFO" "开始监控文件变化: $LOCAL_BACKUP_DIR"
    
    inotifywait -m -r --format '%w%f %e' \
        -e create,modify,delete,move \
        "$LOCAL_BACKUP_DIR" 2>/dev/null | while read file event; do
        
        # 跳过临时文件和目录
        if [[ "$file" == *".tmp" ]] || [[ "$file" == *"~" ]] || [ -d "$file" ]; then
            continue
        fi
        
        log_message "DEBUG" "检测到文件变化: $file ($event)"
        
        case "$event" in
            "CREATE"|"MODIFY"|"MOVED_TO")
                # 等待文件写入完成
                sleep 2
                if [ -f "$file" ]; then
                    sync_file "$file" "modify"
                fi
                ;;
            "DELETE"|"MOVED_FROM")
                sync_file "$file" "delete"
                ;;
        esac
    done
}

# 定时同步（作为备用）
scheduled_sync() {
    log_message "INFO" "启动定时同步任务..."
    
    while true; do
        sleep $SYNC_INTERVAL
        
        # 检查WebDAV连接
        if test_webdav_connection; then
            # 同步最近修改的文件
            find "$LOCAL_BACKUP_DIR" -type f -mmin -$((SYNC_INTERVAL / 60 + 1)) | while read -r file; do
                sync_file "$file" "modify"
            done
        else
            log_message "WARN" "WebDAV连接失败，跳过本次同步"
        fi
    done
}

# 创建PID文件
create_pid_file() {
    if [ -f "$PID_FILE" ]; then
        local old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            log_message "ERROR" "WebDAV同步进程已在运行 (PID: $old_pid)"
            exit 1
        else
            log_message "WARN" "发现僵尸PID文件，清理中..."
            rm -f "$PID_FILE"
        fi
    fi
    
    echo $$ > "$PID_FILE"
}

# 清理PID文件
cleanup_pid_file() {
    rm -f "$PID_FILE"
}

# 信号处理
handle_signal() {
    log_message "INFO" "收到停止信号，正在退出WebDAV同步..."
    cleanup_pid_file
    exit 0
}

# 检查配置
check_config() {
    if [ -z "$WEBDAV_URL" ] || [ -z "$WEBDAV_USERNAME" ] || [ -z "$WEBDAV_PASSWORD" ]; then
        log_message "ERROR" "WebDAV配置不完整，请检查配置参数"
        return 1
    fi
    
    if [ ! -d "$LOCAL_BACKUP_DIR" ]; then
        log_message "ERROR" "本地备份目录不存在: $LOCAL_BACKUP_DIR"
        return 1
    fi
    
    return 0
}

# 显示同步状态
show_sync_status() {
    echo "WebDAV同步状态："
    echo "=================="
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "状态: 运行中 (PID: $pid)"
        else
            echo "状态: 未运行（发现僵尸PID文件）"
            cleanup_pid_file
        fi
    else
        echo "状态: 未运行"
    fi
    
    echo "配置信息："
    echo "  WebDAV服务器: $WEBDAV_URL"
    echo "  远程目录: $WEBDAV_REMOTE_DIR"
    echo "  本地目录: $LOCAL_BACKUP_DIR"
    echo "  同步间隔: ${SYNC_INTERVAL}秒"
    
    # 显示最近的日志
    echo ""
    echo "最近的日志："
    if [ -f "$LOG_FILE" ]; then
        tail -10 "$LOG_FILE"
    else
        echo "  无日志文件"
    fi
}

# ===================主程序===================

# 设置信号处理
trap handle_signal SIGTERM SIGINT

# 检查参数
case "${1:-start}" in
    "start")
        log_message "INFO" "启动WebDAV实时同步服务..."
        
        # 检查配置和依赖
        if ! check_config || ! check_dependencies; then
            exit 1
        fi
        
        # 测试WebDAV连接
        if ! test_webdav_connection; then
            exit 1
        fi
        
        # 创建PID文件
        create_pid_file
        
        # 执行初始全量同步
        log_message "INFO" "执行初始全量同步..."
        full_sync
        
        # 启动实时监控和定时同步
        log_message "INFO" "启动实时监控..."
        monitor_file_changes &
        MONITOR_PID=$!
        
        log_message "INFO" "启动定时同步..."
        scheduled_sync &
        SCHEDULE_PID=$!
        
        # 等待子进程
        wait $MONITOR_PID $SCHEDULE_PID
        ;;
        
    "stop")
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                log_message "INFO" "停止WebDAV同步进程 (PID: $pid)"
                kill "$pid"
                sleep 2
                if kill -0 "$pid" 2>/dev/null; then
                    log_message "WARN" "强制停止WebDAV同步进程"
                    kill -9 "$pid"
                fi
                cleanup_pid_file
                log_message "INFO" "WebDAV同步进程已停止"
            else
                log_message "WARN" "WebDAV同步进程未运行"
                cleanup_pid_file
            fi
        else
            log_message "WARN" "未找到PID文件，WebDAV同步进程可能未运行"
        fi
        ;;
        
    "status")
        show_sync_status
        ;;
        
    "test")
        log_message "INFO" "测试WebDAV连接和配置..."
        if check_config && check_dependencies && test_webdav_connection; then
            log_message "INFO" "WebDAV配置测试通过"
            exit 0
        else
            log_message "ERROR" "WebDAV配置测试失败"
            exit 1
        fi
        ;;
        
    "sync")
        log_message "INFO" "执行手动全量同步..."
        if check_config && check_dependencies && test_webdav_connection; then
            full_sync
        else
            log_message "ERROR" "同步失败，请检查配置"
            exit 1
        fi
        ;;
        
    "restart")
        $0 stop
        sleep 2
        $0 start
        ;;
        
    *)
        echo "用法: $0 {start|stop|status|test|sync|restart}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动WebDAV实时同步"
        echo "  stop    - 停止WebDAV实时同步"
        echo "  status  - 查看同步状态"
        echo "  test    - 测试WebDAV连接"
        echo "  sync    - 手动全量同步"
        echo "  restart - 重启同步服务"
        exit 1
        ;;
esac
