#!/bin/bash
# 配置实时上传策略
# 全量备份每天一次实时上传，增量备份实时上传

echo "=========================================="
echo "配置MySQL备份实时上传策略"
echo "=========================================="

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${YELLOW}[STEP]${NC} $1"
}

# 1. 修改全量备份函数 - 按周几命名
log_step "修改全量备份命名策略..."

# 备份原始脚本
cp /www/backup/scripts/mysql_realtime_backup.sh /www/backup/scripts/mysql_realtime_backup.sh.backup

# 创建新的全量备份函数
cat > /tmp/new_full_backup.txt << 'EOF'
# 全量备份函数（按周几命名，备份后立即上传）
full_backup() {
    log_message "INFO" "开始执行全量备份..."
    
    # 获取星期几和中文名称
    local weekday=$(date +%u)
    local weekday_name=""
    local weekday_cn=""
    
    case $weekday in
        1) weekday_name="Monday"; weekday_cn="周一" ;;
        2) weekday_name="Tuesday"; weekday_cn="周二" ;;
        3) weekday_name="Wednesday"; weekday_cn="周三" ;;
        4) weekday_name="Thursday"; weekday_cn="周四" ;;
        5) weekday_name="Friday"; weekday_cn="周五" ;;
        6) weekday_name="Saturday"; weekday_cn="周六" ;;
        7) weekday_name="Sunday"; weekday_cn="周日" ;;
    esac
    
    # 按周几命名备份文件
    local backup_file="$FULL_BACKUP_DIR/full_backup_${weekday_name}_$(date +%Y%m%d_%H%M%S).sql"
    local start_time=$(date +%s)
    
    # 删除同一天的旧备份（保持最新）
    log_message "INFO" "清理${weekday_cn}的旧备份文件..."
    find "$FULL_BACKUP_DIR" -name "full_backup_${weekday_name}_*.sql*" -type f -delete 2>/dev/null
    
    # 执行mysqldump
    mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --master-data=2 \
        --flush-logs \
        --hex-blob \
        --default-character-set=utf8mb4 \
        $DB_NAME > "$backup_file" 2>>"$ERROR_LOG"
    
    local dump_result=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $dump_result -eq 0 ]; then
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_message "INFO" "全量备份完成: $backup_file (大小: $file_size, 耗时: ${duration}秒)"
        
        # 压缩备份文件
        if [ "$ENABLE_COMPRESSION" = true ]; then
            gzip -$COMPRESSION_LEVEL "$backup_file"
            log_message "INFO" "备份文件已压缩: ${backup_file}.gz"
            backup_file="${backup_file}.gz"
        fi
        
        # 记录备份位置信息
        local position_file="$FULL_BACKUP_DIR/last_full_backup_position.txt"
        mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" > "$position_file"
        
        # 立即上传到WebDAV
        log_message "INFO" "开始上传${weekday_cn}全量备份到云端..."
        if command -v /www/backup/scripts/webdav_sync.sh >/dev/null 2>&1; then
            # 只上传这个新的备份文件
            /www/backup/scripts/upload_single_file.sh "$backup_file"
            if [ $? -eq 0 ]; then
                log_message "INFO" "${weekday_cn}全量备份已成功上传到坚果云"
            else
                log_message "WARN" "${weekday_cn}全量备份上传失败，将在下次定时同步时重试"
            fi
        fi
        
        return 0
    else
        log_message "ERROR" "全量备份失败，请检查错误日志: $ERROR_LOG"
        return 1
    fi
}
EOF

# 2. 创建单文件上传脚本
log_step "创建单文件上传脚本..."

cat > /www/backup/scripts/upload_single_file.sh << 'EOF'
#!/bin/bash
# 单文件WebDAV上传脚本

# 从主配置文件读取WebDAV配置
source /www/backup/scripts/webdav_sync.sh

upload_single_file() {
    local local_file="$1"
    
    if [ ! -f "$local_file" ]; then
        echo "文件不存在: $local_file"
        return 1
    fi
    
    # 计算相对路径
    local relative_path=${local_file#$LOCAL_BACKUP_DIR/}
    local remote_path="$WEBDAV_REMOTE_DIR/$relative_path"
    
    # 上传文件
    local response=$(curl -s -w "%{http_code}" -o /dev/null \
        --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
        --upload-file "$local_file" \
        "$WEBDAV_URL$remote_path" 2>/dev/null)
    
    if [ "$response" = "201" ] || [ "$response" = "204" ]; then
        local file_size=$(du -h "$local_file" | cut -f1)
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 文件上传成功: $local_file (大小: $file_size)" >> /var/log/webdav_sync.log
        return 0
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 文件上传失败: $local_file (HTTP: $response)" >> /var/log/webdav_sync.log
        return 1
    fi
}

# 执行上传
upload_single_file "$1"
EOF

chmod +x /www/backup/scripts/upload_single_file.sh

# 3. 修改增量备份函数，添加实时上传
log_step "修改增量备份，添加实时上传..."

# 在增量备份成功后添加上传逻辑
sed -i '/log_message "INFO" "增量备份文件已压缩:/a\        \
        # 立即上传增量备份到WebDAV\
        log_message "INFO" "开始上传增量备份到云端..."\
        if command -v /www/backup/scripts/upload_single_file.sh >/dev/null 2>&1; then\
            /www/backup/scripts/upload_single_file.sh "${inc_file}.gz"\
            if [ $? -eq 0 ]; then\
                log_message "INFO" "增量备份已成功上传到坚果云"\
            else\
                log_message "WARN" "增量备份上传失败，将在下次定时同步时重试"\
            fi\
        fi' /www/backup/scripts/mysql_realtime_backup.sh

# 4. 修改WebDAV同步策略
log_step "优化WebDAV同步策略..."

# 停止实时WebDAV监控（因为我们改为备份后立即上传）
systemctl stop webdav-sync 2>/dev/null
systemctl disable webdav-sync 2>/dev/null

# 5. 更新定时任务
log_step "更新定时任务..."

# 备份当前定时任务
crontab -l > /tmp/current_cron.backup 2>/dev/null

# 创建新的定时任务
cat > /tmp/optimized_cron << 'EOF'
# MySQL实时备份定时任务（优化版）
0 2 * * * /www/backup/scripts/mysql_realtime_backup.sh full >/dev/null 2>&1
0 */6 * * * /www/backup/scripts/mysql_realtime_backup.sh binlog >/dev/null 2>&1
0 3 * * * /www/backup/scripts/mysql_realtime_backup.sh cleanup >/dev/null 2>&1
# 每周一次完整同步检查（确保云端文件完整）
0 4 * * 1 /www/backup/scripts/webdav_sync.sh sync >/dev/null 2>&1
EOF

# 保留其他非备份相关的定时任务
if [ -f /tmp/current_cron.backup ]; then
    grep -v "mysql_realtime_backup.sh\|webdav_sync.sh" /tmp/current_cron.backup >> /tmp/optimized_cron 2>/dev/null
fi

# 应用新的定时任务
crontab /tmp/optimized_cron

# 6. 测试新配置
log_step "测试新的备份配置..."

echo "执行测试备份..."
/www/backup/scripts/mysql_realtime_backup.sh full

echo ""
echo "当前备份文件："
ls -la /www/backup/mysql/full/

echo ""
echo "=========================================="
log_info "配置完成！"
echo "=========================================="
echo ""
echo "新的备份策略："
echo "✅ 全量备份：每天凌晨2点，按周几命名，立即上传"
echo "✅ 增量备份：数据变化时自动生成，立即上传"  
echo "✅ 文件管理：每个星期几只保留最新的全量备份"
echo "✅ 云端同步：备份完成立即上传，每周一次完整性检查"
echo ""
echo "预计月度流量使用："
echo "- 全量备份: 646KB × 30天 = 19MB"
echo "- 增量备份: 约50KB × 平均次数 = 5-10MB"  
echo "- 总计: 约25-30MB/月（远低于1GB限制）"
echo ""
echo "备份文件命名示例："
echo "- full_backup_Monday_20250813_020000.sql.gz"
echo "- full_backup_Tuesday_20250814_020000.sql.gz"
echo "- incremental_20250813_143022.sql.gz"
echo ""
log_info "您的魔方财务系统现在拥有最优的实时备份策略！"
