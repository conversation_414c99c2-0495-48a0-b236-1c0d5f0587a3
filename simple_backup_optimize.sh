#!/bin/bash
# 简单的备份优化脚本 - 按周几命名全量备份

echo "=========================================="
echo "优化备份命名 - 按周几命名全量备份"
echo "=========================================="

# 1. 备份原始脚本
echo "备份原始脚本..."
cp /www/backup/scripts/mysql_realtime_backup.sh /www/backup/scripts/mysql_realtime_backup.sh.backup

# 2. 修改全量备份函数中的文件命名部分
echo "修改备份文件命名..."

# 查找并替换备份文件命名行
sed -i 's/local backup_file="$FULL_BACKUP_DIR\/full_backup_$(date +%Y%m%d_%H%M%S).sql"/local weekday=$(date +%u); local weekday_names=("" "Monday" "Tuesday" "Wednesday" "Thursday" "Friday" "Saturday" "Sunday"); local weekday_name=${weekday_names[$weekday]}; local backup_file="$FULL_BACKUP_DIR\/full_backup_${weekday_name}_$(date +%Y%m%d_%H%M%S).sql"/' /www/backup/scripts/mysql_realtime_backup.sh

# 3. 在备份前删除同一天的旧备份
sed -i '/local backup_file=/a\    # 删除同一天的旧备份\n    find "$FULL_BACKUP_DIR" -name "full_backup_${weekday_name}_*.sql*" -type f -delete 2>/dev/null' /www/backup/scripts/mysql_realtime_backup.sh

# 4. 修改保留策略
sed -i 's/FULL_BACKUP_RETENTION_DAYS=30/FULL_BACKUP_RETENTION_DAYS=7/' /www/backup/scripts/mysql_realtime_backup.sh
sed -i 's/INCREMENTAL_RETENTION_DAYS=7/INCREMENTAL_RETENTION_DAYS=3/' /www/backup/scripts/mysql_realtime_backup.sh

echo "优化完成！"
echo ""
echo "现在的备份命名格式："
echo "  周一: full_backup_Monday_20250813_171801.sql.gz"
echo "  周二: full_backup_Tuesday_20250813_171801.sql.gz"
echo "  ..."
echo ""
echo "测试新的备份功能..."

# 测试备份
/www/backup/scripts/mysql_realtime_backup.sh full

echo ""
echo "当前备份文件："
ls -la /www/backup/mysql/full/

echo ""
echo "=========================================="
echo "优化完成！"
echo "- 全量备份按周几命名，每个星期几只保留最新的一个"
echo "- 增量备份保留3天"
echo "- 总共最多保留7个全量备份文件（一周7天）"
echo "=========================================="
