#!/bin/bash
# MySQL 5.6 用户创建修复脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=========================================="
echo "MySQL 5.6 备份用户创建修复脚本"
echo "=========================================="

# 获取配置信息
read -p "MySQL root密码: " -s MYSQL_ROOT_PASS
echo ""
read -p "备份用户名 (默认: backup_user): " BACKUP_USER
BACKUP_USER=${BACKUP_USER:-backup_user}
read -p "备份用户密码: " -s BACKUP_PASS
echo ""

# 验证MySQL连接
log_info "验证MySQL连接..."
if ! mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
    log_error "MySQL连接失败，请检查root密码"
    exit 1
fi
log_info "MySQL连接验证成功"

# 创建备份用户（兼容MySQL 5.6）
log_info "创建备份用户 $BACKUP_USER..."

# 先检查用户是否存在
user_exists=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT COUNT(*) FROM mysql.user WHERE User='$BACKUP_USER' AND Host='localhost';" 2>/dev/null | tail -1)

if [ "$user_exists" = "1" ]; then
    log_info "删除已存在的备份用户..."
    mysql -u root -p"$MYSQL_ROOT_PASS" -e "DROP USER '$BACKUP_USER'@'localhost';" 2>/dev/null
fi

# 创建新用户
mysql -u root -p"$MYSQL_ROOT_PASS" << EOF
-- 创建备份用户
CREATE USER '$BACKUP_USER'@'localhost' IDENTIFIED BY '$BACKUP_PASS';

-- 授予权限
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON *.* TO '$BACKUP_USER'@'localhost';
GRANT REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO '$BACKUP_USER'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
EOF

if [ $? -eq 0 ]; then
    log_info "备份用户创建成功"
    
    # 验证用户权限
    log_info "验证用户权限..."
    if mysql -u "$BACKUP_USER" -p"$BACKUP_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
        log_info "用户权限验证成功"
        
        echo ""
        echo "=========================================="
        log_info "备份用户修复完成！"
        echo "=========================================="
        echo ""
        echo "用户信息："
        echo "  用户名: $BACKUP_USER"
        echo "  主机: localhost"
        echo "  权限: SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER, REPLICATION SLAVE, REPLICATION CLIENT"
        echo ""
        echo "现在您可以继续运行部署脚本，或者手动继续配置。"
        echo ""
        echo "如果要继续自动部署，请运行："
        echo "  ./deploy_mysql_backup_with_webdav.sh"
        echo ""
        echo "如果要手动继续，请按以下步骤："
        echo "1. 创建备份目录: mkdir -p /www/backup/mysql/{full,incremental,binlog}"
        echo "2. 部署备份脚本到 /www/backup/scripts/"
        echo "3. 配置systemd服务"
        echo "4. 设置定时任务"
        
    else
        log_error "用户权限验证失败"
        exit 1
    fi
else
    log_error "备份用户创建失败"
    exit 1
fi
