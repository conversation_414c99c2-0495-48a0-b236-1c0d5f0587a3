# MySQL 5.6.50 实时备份完整配置指南

## 1. 启用MySQL Binlog

### 修改MySQL配置文件
宝塔面板MySQL配置文件位置：`/etc/my.cnf`

```bash
# 备份原配置文件
cp /etc/my.cnf /etc/my.cnf.backup

# 编辑配置文件
vi /etc/my.cnf
```

在 `[mysqld]` 部分添加以下配置：

```ini
[mysqld]
# 启用二进制日志
log-bin=mysql-bin
server-id=1
binlog-format=ROW
expire_logs_days=7
max_binlog_size=100M
binlog-do-db=your_database_name

# 可选：启用慢查询日志
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2
```

### 重启MySQL服务
```bash
# 宝塔面板重启MySQL
/etc/init.d/mysqld restart

# 或者通过宝塔面板界面重启
```

### 验证Binlog是否启用
```sql
-- 登录MySQL
mysql -u root -p

-- 检查binlog状态
SHOW VARIABLES LIKE 'log_bin';
SHOW MASTER STATUS;
SHOW BINARY LOGS;
```

## 2. 创建备份用户

```sql
-- 创建专用备份用户
CREATE USER 'backup_user'@'localhost' IDENTIFIED BY 'strong_password_123';

-- 授予必要权限
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON *.* TO 'backup_user'@'localhost';
GRANT REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'backup_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
```

## 3. 实时备份脚本

### 主备份脚本
```bash
#!/bin/bash
# 文件名: mysql_realtime_backup.sh

# 配置参数
DB_USER="backup_user"
DB_PASS="strong_password_123"
DB_NAME="your_database_name"  # 替换为您的数据库名
BACKUP_DIR="/www/backup/mysql"
LOG_FILE="/var/log/mysql_backup.log"
REMOTE_SERVER="<EMAIL>"  # 可选：远程备份服务器
REMOTE_DIR="/backup/mysql"

# 创建备份目录
mkdir -p $BACKUP_DIR/{full,incremental,binlog}

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

# 全量备份函数
full_backup() {
    local backup_file="$BACKUP_DIR/full/full_backup_$(date +%Y%m%d_%H%M%S).sql"
    log_message "开始全量备份..."
    
    mysqldump -u$DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --master-data=2 \
        --flush-logs \
        $DB_NAME > $backup_file
    
    if [ $? -eq 0 ]; then
        log_message "全量备份完成: $backup_file"
        gzip $backup_file
        log_message "备份文件已压缩"
    else
        log_message "全量备份失败!"
        return 1
    fi
}

# Binlog备份函数
backup_binlog() {
    local binlog_dir="$BACKUP_DIR/binlog"
    log_message "开始备份Binlog..."
    
    # 获取当前binlog文件列表
    mysql -u$DB_USER -p$DB_PASS -e "SHOW BINARY LOGS;" | grep mysql-bin | while read binlog size; do
        local binlog_file="/var/lib/mysql/$binlog"
        local backup_binlog="$binlog_dir/$binlog"
        
        if [ -f "$binlog_file" ] && [ ! -f "$backup_binlog" ]; then
            cp "$binlog_file" "$backup_binlog"
            log_message "备份Binlog: $binlog"
        fi
    done
}

# 增量备份函数（基于binlog）
incremental_backup() {
    local inc_dir="$BACKUP_DIR/incremental"
    local last_pos_file="$inc_dir/last_position.txt"
    
    # 读取上次备份位置
    if [ -f "$last_pos_file" ]; then
        local last_file=$(cat $last_pos_file | cut -d: -f1)
        local last_pos=$(cat $last_pos_file | cut -d: -f2)
    else
        # 首次运行，获取当前位置
        local current_status=$(mysql -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" | tail -n 1)
        local last_file=$(echo $current_status | awk '{print $1}')
        local last_pos=$(echo $current_status | awk '{print $2}')
    fi
    
    # 获取当前位置
    local current_status=$(mysql -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" | tail -n 1)
    local current_file=$(echo $current_status | awk '{print $1}')
    local current_pos=$(echo $current_status | awk '{print $2}')
    
    # 如果位置有变化，进行增量备份
    if [ "$last_file:$last_pos" != "$current_file:$current_pos" ]; then
        local inc_file="$inc_dir/incremental_$(date +%Y%m%d_%H%M%S).sql"
        
        mysqlbinlog --start-position=$last_pos /var/lib/mysql/$last_file > $inc_file
        
        if [ "$last_file" != "$current_file" ]; then
            mysqlbinlog /var/lib/mysql/$current_file >> $inc_file
        fi
        
        log_message "增量备份完成: $inc_file"
        echo "$current_file:$current_pos" > $last_pos_file
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_message "清理7天前的备份文件..."
    find $BACKUP_DIR -name "*.sql*" -mtime +7 -delete
    find $BACKUP_DIR -name "mysql-bin.*" -mtime +7 -delete
}

# 远程同步（可选）
sync_to_remote() {
    if [ ! -z "$REMOTE_SERVER" ]; then
        log_message "同步到远程服务器..."
        rsync -avz --delete $BACKUP_DIR/ $REMOTE_SERVER:$REMOTE_DIR/
        log_message "远程同步完成"
    fi
}

# 主执行逻辑
case "$1" in
    "full")
        full_backup
        ;;
    "incremental")
        incremental_backup
        ;;
    "binlog")
        backup_binlog
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    "sync")
        sync_to_remote
        ;;
    *)
        echo "用法: $0 {full|incremental|binlog|cleanup|sync}"
        exit 1
        ;;
esac
```

## 4. 实时监控脚本

### Binlog实时监控
```bash
#!/bin/bash
# 文件名: mysql_realtime_monitor.sh

DB_USER="backup_user"
DB_PASS="strong_password_123"
BACKUP_DIR="/www/backup/mysql"
LOG_FILE="/var/log/mysql_realtime.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

# 实时监控binlog变化
monitor_binlog() {
    local last_size=0
    local binlog_file="/var/lib/mysql/mysql-bin.index"
    
    log_message "开始实时监控MySQL Binlog..."
    
    while true; do
        # 检查binlog index文件变化
        if [ -f "$binlog_file" ]; then
            local current_size=$(stat -c%s "$binlog_file")
            
            if [ $current_size -ne $last_size ]; then
                log_message "检测到Binlog变化，执行增量备份..."
                /www/backup/scripts/mysql_realtime_backup.sh incremental
                last_size=$current_size
            fi
        fi
        
        sleep 10  # 每10秒检查一次
    done
}

# 启动监控
monitor_binlog
```

## 5. 定时任务配置

### 在宝塔面板中设置计划任务

1. 登录宝塔面板
2. 进入"计划任务"
3. 添加以下任务：

```bash
# 每天凌晨2点全量备份
0 2 * * * /www/backup/scripts/mysql_realtime_backup.sh full

# 每小时增量备份
0 * * * * /www/backup/scripts/mysql_realtime_backup.sh incremental

# 每30分钟备份binlog
*/30 * * * * /www/backup/scripts/mysql_realtime_backup.sh binlog

# 每天清理旧备份
0 3 * * * /www/backup/scripts/mysql_realtime_backup.sh cleanup

# 每小时同步到远程（可选）
0 * * * * /www/backup/scripts/mysql_realtime_backup.sh sync
```

## 6. 系统服务配置

### 创建systemd服务
```bash
# 创建服务文件
vi /etc/systemd/system/mysql-realtime-backup.service
```

服务文件内容：
```ini
[Unit]
Description=MySQL Real-time Backup Monitor
After=mysqld.service
Requires=mysqld.service

[Service]
Type=simple
User=root
ExecStart=/www/backup/scripts/mysql_realtime_monitor.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
systemctl daemon-reload
systemctl enable mysql-realtime-backup
systemctl start mysql-realtime-backup
systemctl status mysql-realtime-backup
```
