#!/bin/bash
# MySQL实时备份系统 - 最新优化版本部署脚本
# 特性：按周几命名全量备份，实时上传，流量优化

echo "=========================================="
echo "MySQL实时备份系统 - 最新优化版本"
echo "特性：按周几命名 + 实时上传 + 流量优化"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root用户运行此脚本"
    exit 1
fi

# 检查宝塔面板
if [ ! -f "/etc/init.d/bt" ]; then
    log_error "未检测到宝塔面板，请先安装宝塔面板"
    exit 1
fi

# 检查MySQL
if ! command -v mysql &> /dev/null; then
    log_error "未检测到MySQL，请先在宝塔面板中安装MySQL"
    exit 1
fi

# 安装依赖
log_step "安装必要依赖..."
yum install -y curl inotify-tools rsync gzip >/dev/null 2>&1
log_info "依赖安装完成"

# 获取配置信息
log_step "配置信息收集"
read -p "MySQL root密码: " -s MYSQL_ROOT_PASS; echo
read -p "要备份的数据库名: " DB_NAME
read -p "备份用户名 (默认: backup_user): " BACKUP_USER
BACKUP_USER=${BACKUP_USER:-backup_user}
read -p "备份用户密码: " -s BACKUP_PASS; echo
read -p "备份目录 (默认: /www/backup): " BACKUP_DIR
BACKUP_DIR=${BACKUP_DIR:-/www/backup}

# WebDAV配置
read -p "是否启用WebDAV同步? (y/n, 默认: y): " ENABLE_WEBDAV
ENABLE_WEBDAV=${ENABLE_WEBDAV:-y}

if [ "$ENABLE_WEBDAV" = "y" ]; then
    echo "推荐使用坚果云WebDAV (免费3GB空间，国内访问快)"
    read -p "WebDAV服务器URL (坚果云: https://dav.jianguoyun.com/dav/): " WEBDAV_URL
    WEBDAV_URL=${WEBDAV_URL:-https://dav.jianguoyun.com/dav/}
    read -p "WebDAV用户名 (坚果云邮箱): " WEBDAV_USERNAME
    read -p "WebDAV密码 (坚果云应用密码): " -s WEBDAV_PASSWORD; echo
    read -p "远程目录名 (默认: mysql_backup): " WEBDAV_REMOTE_DIR
    WEBDAV_REMOTE_DIR=${WEBDAV_REMOTE_DIR:-mysql_backup}
fi

# 验证MySQL连接
log_step "验证MySQL连接..."
if ! mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
    log_error "MySQL连接失败，请检查root密码"
    exit 1
fi

# 检查数据库
db_exists=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SHOW DATABASES LIKE '$DB_NAME';" 2>/dev/null | grep -c "$DB_NAME")
if [ $db_exists -eq 0 ]; then
    log_error "数据库 $DB_NAME 不存在"
    exit 1
fi

# 启用MySQL Binlog
log_step "配置MySQL Binlog..."
mysql_config="/etc/my.cnf"
if ! grep -q "^log-bin" "$mysql_config"; then
    cp "$mysql_config" "${mysql_config}.backup.$(date +%Y%m%d_%H%M%S)"
    cat >> "$mysql_config" << EOF

# MySQL实时备份配置
log-bin=mysql-bin
server-id=1
binlog-format=ROW
expire_logs_days=7
max_binlog_size=100M
binlog-do-db=$DB_NAME
EOF
    /etc/init.d/mysqld restart
    log_info "MySQL Binlog已启用并重启服务"
else
    log_info "MySQL Binlog已启用"
fi

# 创建备份用户
log_step "创建备份用户..."
user_exists=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT COUNT(*) FROM mysql.user WHERE User='$BACKUP_USER' AND Host='localhost';" 2>/dev/null | tail -1)
if [ "$user_exists" = "1" ]; then
    mysql -u root -p"$MYSQL_ROOT_PASS" -e "DROP USER '$BACKUP_USER'@'localhost';" 2>/dev/null
fi

mysql -u root -p"$MYSQL_ROOT_PASS" << EOF
CREATE USER '$BACKUP_USER'@'localhost' IDENTIFIED BY '$BACKUP_PASS';
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER, RELOAD ON *.* TO '$BACKUP_USER'@'localhost';
GRANT REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO '$BACKUP_USER'@'localhost';
GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$BACKUP_USER'@'localhost';
FLUSH PRIVILEGES;
EOF
log_info "备份用户创建成功"

# 创建目录结构
log_step "创建目录结构..."
mkdir -p "$BACKUP_DIR"/{mysql/{full,incremental,binlog},scripts,logs}
chmod 755 "$BACKUP_DIR"
chmod 700 "$BACKUP_DIR/mysql"

# 创建优化的备份脚本
log_step "创建优化备份脚本..."
cat > "$BACKUP_DIR/scripts/mysql_realtime_backup.sh" << 'SCRIPT_EOF'
#!/bin/bash
# MySQL实时备份脚本 - 优化版本

# 配置参数
DB_USER="PLACEHOLDER_DB_USER"
DB_PASS="PLACEHOLDER_DB_PASS"
DB_NAME="PLACEHOLDER_DB_NAME"
DB_HOST="localhost"
DB_PORT="3306"

BACKUP_BASE_DIR="PLACEHOLDER_BACKUP_DIR/mysql"
FULL_BACKUP_DIR="$BACKUP_BASE_DIR/full"
INCREMENTAL_DIR="$BACKUP_BASE_DIR/incremental"
BINLOG_DIR="$BACKUP_BASE_DIR/binlog"

LOG_FILE="/var/log/mysql_backup.log"
ERROR_LOG="/var/log/mysql_backup_error.log"

FULL_BACKUP_RETENTION_DAYS=7
INCREMENTAL_RETENTION_DAYS=3
BINLOG_RETENTION_DAYS=3

ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=9

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a $LOG_FILE
}

# 检查MySQL连接
check_mysql_connection() {
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SELECT 1;" >/dev/null 2>&1
    return $?
}

# 全量备份函数（按周几命名）
full_backup() {
    log_message "INFO" "开始执行全量备份..."
    
    # 获取星期几
    local weekday=$(date +%u)
    local weekday_names=("" "Monday" "Tuesday" "Wednesday" "Thursday" "Friday" "Saturday" "Sunday")
    local weekday_name=${weekday_names[$weekday]}
    local weekday_cn=("" "周一" "周二" "周三" "周四" "周五" "周六" "周日")
    local weekday_cn_name=${weekday_cn[$weekday]}
    
    # 按周几命名备份文件
    local backup_file="$FULL_BACKUP_DIR/full_backup_${weekday_name}_$(date +%Y%m%d_%H%M%S).sql"
    local start_time=$(date +%s)
    
    # 删除同一天的旧备份
    log_message "INFO" "清理${weekday_cn_name}的旧备份文件..."
    find "$FULL_BACKUP_DIR" -name "full_backup_${weekday_name}_*.sql*" -type f -delete 2>/dev/null
    
    # 执行mysqldump
    mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --master-data=2 \
        --flush-logs \
        --hex-blob \
        --default-character-set=utf8mb4 \
        $DB_NAME > "$backup_file" 2>>"$ERROR_LOG"
    
    local dump_result=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $dump_result -eq 0 ]; then
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_message "INFO" "全量备份完成: $backup_file (大小: $file_size, 耗时: ${duration}秒)"
        
        # 压缩备份文件
        if [ "$ENABLE_COMPRESSION" = true ]; then
            gzip -$COMPRESSION_LEVEL "$backup_file"
            log_message "INFO" "备份文件已压缩: ${backup_file}.gz"
            backup_file="${backup_file}.gz"
        fi
        
        # 记录备份位置
        local position_file="$FULL_BACKUP_DIR/last_full_backup_position.txt"
        mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" > "$position_file"
        
        # 立即上传到WebDAV
        if [ -f "/www/backup/scripts/upload_single_file.sh" ]; then
            log_message "INFO" "开始上传${weekday_cn_name}全量备份到云端..."
            /www/backup/scripts/upload_single_file.sh "$backup_file"
            if [ $? -eq 0 ]; then
                log_message "INFO" "${weekday_cn_name}全量备份已成功上传到云端"
            else
                log_message "WARN" "${weekday_cn_name}全量备份上传失败，将在下次同步时重试"
            fi
        fi
        
        return 0
    else
        log_message "ERROR" "全量备份失败，请检查错误日志: $ERROR_LOG"
        return 1
    fi
}

# 增量备份函数
incremental_backup() {
    log_message "INFO" "开始执行增量备份..."
    
    local position_file="$INCREMENTAL_DIR/last_incremental_position.txt"
    local current_status_file="$INCREMENTAL_DIR/current_status.tmp"
    
    # 获取当前master状态
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" | tail -n 1 > "$current_status_file"
    local current_file=$(cat "$current_status_file" | awk '{print $1}')
    local current_pos=$(cat "$current_status_file" | awk '{print $2}')
    
    # 读取上次备份位置
    if [ -f "$position_file" ]; then
        local last_file=$(cat "$position_file" | cut -d: -f1)
        local last_pos=$(cat "$position_file" | cut -d: -f2)
    else
        local full_position_file="$FULL_BACKUP_DIR/last_full_backup_position.txt"
        if [ -f "$full_position_file" ]; then
            local last_file=$(tail -n 1 "$full_position_file" | awk '{print $1}')
            local last_pos=$(tail -n 1 "$full_position_file" | awk '{print $2}')
        else
            log_message "WARN" "未找到基准位置，跳过增量备份"
            return 1
        fi
    fi
    
    # 检查是否有变化
    if [ "$last_file:$last_pos" = "$current_file:$current_pos" ]; then
        log_message "INFO" "数据库无变化，跳过增量备份"
        return 0
    fi
    
    local inc_file="$INCREMENTAL_DIR/incremental_$(date +%Y%m%d_%H%M%S).sql"
    local start_time=$(date +%s)
    
    # 导出binlog增量数据
    if [ "$last_file" = "$current_file" ]; then
        mysqlbinlog --start-position=$last_pos --stop-position=$current_pos \
            /var/lib/mysql/$last_file > "$inc_file" 2>>"$ERROR_LOG"
    else
        mysqlbinlog --start-position=$last_pos /var/lib/mysql/$last_file > "$inc_file" 2>>"$ERROR_LOG"
        mysqlbinlog --stop-position=$current_pos /var/lib/mysql/$current_file >> "$inc_file" 2>>"$ERROR_LOG"
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $? -eq 0 ] && [ -s "$inc_file" ]; then
        local file_size=$(du -h "$inc_file" | cut -f1)
        log_message "INFO" "增量备份完成: $inc_file (大小: $file_size, 耗时: ${duration}秒)"
        
        # 压缩备份文件
        if [ "$ENABLE_COMPRESSION" = true ]; then
            gzip -$COMPRESSION_LEVEL "$inc_file"
            log_message "INFO" "增量备份文件已压缩: ${inc_file}.gz"
            
            # 立即上传增量备份
            if [ -f "/www/backup/scripts/upload_single_file.sh" ]; then
                log_message "INFO" "开始上传增量备份到云端..."
                /www/backup/scripts/upload_single_file.sh "${inc_file}.gz"
                if [ $? -eq 0 ]; then
                    log_message "INFO" "增量备份已成功上传到云端"
                else
                    log_message "WARN" "增量备份上传失败，将在下次同步时重试"
                fi
            fi
        fi
        
        # 更新位置记录
        echo "$current_file:$current_pos" > "$position_file"
        return 0
    else
        log_message "ERROR" "增量备份失败或无数据变化"
        rm -f "$inc_file"
        return 1
    fi
}

# Binlog备份
backup_binlog_files() {
    log_message "INFO" "开始备份Binlog文件..."
    local mysql_data_dir="/var/lib/mysql"
    local binlog_list=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW BINARY LOGS;" | awk '{print $1}' | grep -v Log_name)
    
    for binlog in $binlog_list; do
        local source_file="$mysql_data_dir/$binlog"
        local dest_file="$BINLOG_DIR/$binlog"
        
        if [ -f "$source_file" ] && [ ! -f "$dest_file" ]; then
            cp "$source_file" "$dest_file"
            log_message "INFO" "备份Binlog文件: $binlog"
        fi
    done
}

# 清理旧备份
cleanup_old_backups() {
    log_message "INFO" "开始清理旧备份文件..."
    find "$FULL_BACKUP_DIR" -name "full_backup_*_*.sql*" -mtime +$FULL_BACKUP_RETENTION_DAYS -delete
    find "$INCREMENTAL_DIR" -name "incremental_*.sql*" -mtime +$INCREMENTAL_RETENTION_DAYS -delete
    find "$BINLOG_DIR" -name "mysql-bin.*" -mtime +$BINLOG_RETENTION_DAYS -delete
    log_message "INFO" "旧备份文件清理完成"
}

# 检查备份状态
check_backup_status() {
    log_message "INFO" "检查备份状态..."
    local latest_full=$(find "$FULL_BACKUP_DIR" -name "full_backup_*.sql*" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    if [ ! -z "$latest_full" ]; then
        local full_age=$(( ($(date +%s) - $(stat -c %Y "$latest_full")) / 86400 ))
        log_message "INFO" "最新全量备份: $latest_full (${full_age}天前)"
    fi
}

# 初始化
init_backup() {
    log_message "INFO" "初始化备份环境..."
    mkdir -p "$FULL_BACKUP_DIR" "$INCREMENTAL_DIR" "$BINLOG_DIR"
    
    if ! check_mysql_connection; then
        log_message "ERROR" "MySQL连接失败"
        return 1
    fi
    
    local binlog_status=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW VARIABLES LIKE 'log_bin';" | grep log_bin | awk '{print $2}')
    if [ "$binlog_status" != "ON" ]; then
        log_message "ERROR" "MySQL Binlog未启用"
        return 1
    fi
    
    log_message "INFO" "备份环境初始化完成"
    return 0
}

# 主程序
case "${1:-help}" in
    "full") full_backup ;;
    "incremental") incremental_backup ;;
    "binlog") backup_binlog_files ;;
    "cleanup") cleanup_old_backups ;;
    "status") check_backup_status ;;
    "init") init_backup ;;
    *) 
        echo "用法: $0 {full|incremental|binlog|cleanup|status|init}"
        exit 1 ;;
esac

exit $?
SCRIPT_EOF

# 替换配置参数
sed -i "s/PLACEHOLDER_DB_USER/$BACKUP_USER/g" "$BACKUP_DIR/scripts/mysql_realtime_backup.sh"
sed -i "s/PLACEHOLDER_DB_PASS/$BACKUP_PASS/g" "$BACKUP_DIR/scripts/mysql_realtime_backup.sh"
sed -i "s/PLACEHOLDER_DB_NAME/$DB_NAME/g" "$BACKUP_DIR/scripts/mysql_realtime_backup.sh"
sed -i "s|PLACEHOLDER_BACKUP_DIR|$BACKUP_DIR|g" "$BACKUP_DIR/scripts/mysql_realtime_backup.sh"

chmod +x "$BACKUP_DIR/scripts/mysql_realtime_backup.sh"
log_info "MySQL备份脚本创建完成"

# 创建WebDAV上传脚本
if [ "$ENABLE_WEBDAV" = "y" ]; then
    log_step "创建WebDAV上传脚本..."

    cat > "$BACKUP_DIR/scripts/upload_single_file.sh" << 'UPLOAD_EOF'
#!/bin/bash
# 单文件WebDAV上传脚本

WEBDAV_URL="PLACEHOLDER_WEBDAV_URL"
WEBDAV_USERNAME="PLACEHOLDER_WEBDAV_USERNAME"
WEBDAV_PASSWORD="PLACEHOLDER_WEBDAV_PASSWORD"
WEBDAV_REMOTE_DIR="PLACEHOLDER_WEBDAV_REMOTE_DIR"
LOCAL_BACKUP_DIR="PLACEHOLDER_LOCAL_BACKUP_DIR"

upload_single_file() {
    local local_file="$1"

    if [ ! -f "$local_file" ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 文件不存在: $local_file" >> /var/log/webdav_sync.log
        return 1
    fi

    # 计算相对路径
    local relative_path=${local_file#$LOCAL_BACKUP_DIR/}
    local remote_path="$WEBDAV_REMOTE_DIR/$relative_path"

    # 确保远程目录存在 - 逐级创建
    local remote_dir=$(dirname "$remote_path")
    if [ "$remote_dir" != "." ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 创建远程目录: $remote_dir" >> /var/log/webdav_sync.log

        # 创建主目录
        curl -s --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
             --request MKCOL "$WEBDAV_URL$WEBDAV_REMOTE_DIR/" >/dev/null 2>&1

        # 创建子目录
        IFS='/' read -ra DIR_PARTS <<< "$remote_dir"
        local current_path=""
        for part in "${DIR_PARTS[@]}"; do
            if [ ! -z "$part" ]; then
                if [ -z "$current_path" ]; then
                    current_path="$part"
                else
                    current_path="$current_path/$part"
                fi
                curl -s --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
                     --request MKCOL "$WEBDAV_URL$current_path/" >/dev/null 2>&1
            fi
        done
    fi

    # 上传文件 - 添加重试机制
    local max_retries=3
    local retry_count=0
    local response=""

    while [ $retry_count -lt $max_retries ]; do
        retry_count=$((retry_count + 1))
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 上传尝试 $retry_count/$max_retries: $local_file" >> /var/log/webdav_sync.log

        response=$(curl -s -w "%{http_code}" -o /dev/null \
            --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
            --upload-file "$local_file" \
            "$WEBDAV_URL$remote_path" 2>/dev/null)

        echo "$(date '+%Y-%m-%d %H:%M:%S') - 响应码: $response" >> /var/log/webdav_sync.log

        if [ "$response" = "201" ] || [ "$response" = "204" ]; then
            break
        elif [ "$response" = "409" ]; then
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 目录冲突，重新创建目录..." >> /var/log/webdav_sync.log
            sleep 2
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 上传失败 HTTP:$response，等待重试..." >> /var/log/webdav_sync.log
            sleep 3
        fi
    done

    if [ "$response" = "201" ] || [ "$response" = "204" ]; then
        local file_size=$(du -h "$local_file" | cut -f1)
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 文件上传成功: $local_file (大小: $file_size)" >> /var/log/webdav_sync.log
        return 0
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 文件上传失败: $local_file (HTTP: $response)" >> /var/log/webdav_sync.log
        return 1
    fi
}

upload_single_file "$1"
UPLOAD_EOF

    # 替换WebDAV配置
    sed -i "s|PLACEHOLDER_WEBDAV_URL|$WEBDAV_URL|g" "$BACKUP_DIR/scripts/upload_single_file.sh"
    sed -i "s/PLACEHOLDER_WEBDAV_USERNAME/$WEBDAV_USERNAME/g" "$BACKUP_DIR/scripts/upload_single_file.sh"
    sed -i "s/PLACEHOLDER_WEBDAV_PASSWORD/$WEBDAV_PASSWORD/g" "$BACKUP_DIR/scripts/upload_single_file.sh"
    sed -i "s/PLACEHOLDER_WEBDAV_REMOTE_DIR/$WEBDAV_REMOTE_DIR/g" "$BACKUP_DIR/scripts/upload_single_file.sh"
    sed -i "s|PLACEHOLDER_LOCAL_BACKUP_DIR|$BACKUP_DIR/mysql|g" "$BACKUP_DIR/scripts/upload_single_file.sh"

    chmod +x "$BACKUP_DIR/scripts/upload_single_file.sh"
    log_info "WebDAV上传脚本创建完成"
fi

# 创建监控脚本
log_step "创建监控脚本..."

cat > "$BACKUP_DIR/scripts/mysql_realtime_monitor.sh" << 'MONITOR_EOF'
#!/bin/bash
# MySQL实时监控脚本

DB_USER="PLACEHOLDER_DB_USER"
DB_PASS="PLACEHOLDER_DB_PASS"
DB_NAME="PLACEHOLDER_DB_NAME"
DB_HOST="localhost"
DB_PORT="3306"

MONITOR_INTERVAL=30
BACKUP_SCRIPT="PLACEHOLDER_BACKUP_DIR/scripts/mysql_realtime_backup.sh"
LOG_FILE="/var/log/mysql_realtime_monitor.log"
PID_FILE="/var/run/mysql_realtime_monitor.pid"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

check_mysql_connection() {
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SELECT 1;" >/dev/null 2>&1
    return $?
}

get_current_position() {
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS;" 2>/dev/null | tail -n 1
}

check_binlog_changes() {
    local current_position=$(get_current_position)
    local current_file=$(echo "$current_position" | awk '{print $1}')
    local current_pos=$(echo "$current_position" | awk '{print $2}')

    local last_position_file="/tmp/mysql_monitor_last_position"
    if [ -f "$last_position_file" ]; then
        local last_position=$(cat "$last_position_file")
        local last_file=$(echo "$last_position" | cut -d: -f1)
        local last_pos=$(echo "$last_position" | cut -d: -f2)
    else
        echo "$current_file:$current_pos" > "$last_position_file"
        return 1
    fi

    if [ "$last_file:$last_pos" != "$current_file:$current_pos" ]; then
        echo "$current_file:$current_pos" > "$last_position_file"
        return 0
    fi

    return 1
}

main_monitor_loop() {
    log_message "MySQL实时监控启动 (PID: $$)"

    while true; do
        if ! check_mysql_connection; then
            log_message "MySQL连接丢失，等待重连..."
            sleep $MONITOR_INTERVAL
            continue
        fi

        if check_binlog_changes; then
            log_message "检测到数据库变化，触发增量备份..."
            $BACKUP_SCRIPT incremental
        fi

        sleep $MONITOR_INTERVAL
    done
}

case "${1:-start}" in
    "start")
        if [ -f "$PID_FILE" ]; then
            local old_pid=$(cat "$PID_FILE")
            if kill -0 "$old_pid" 2>/dev/null; then
                echo "监控进程已在运行 (PID: $old_pid)"
                exit 1
            fi
        fi
        echo $$ > "$PID_FILE"
        main_monitor_loop
        ;;
    "stop")
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                rm -f "$PID_FILE"
                echo "监控进程已停止"
            fi
        fi
        ;;
    "status")
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                echo "监控进程正在运行 (PID: $pid)"
            else
                echo "监控进程未运行"
                rm -f "$PID_FILE"
            fi
        else
            echo "监控进程未运行"
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|status}"
        exit 1
        ;;
esac
MONITOR_EOF

# 替换监控脚本配置
sed -i "s/PLACEHOLDER_DB_USER/$BACKUP_USER/g" "$BACKUP_DIR/scripts/mysql_realtime_monitor.sh"
sed -i "s/PLACEHOLDER_DB_PASS/$BACKUP_PASS/g" "$BACKUP_DIR/scripts/mysql_realtime_monitor.sh"
sed -i "s/PLACEHOLDER_DB_NAME/$DB_NAME/g" "$BACKUP_DIR/scripts/mysql_realtime_monitor.sh"
sed -i "s|PLACEHOLDER_BACKUP_DIR|$BACKUP_DIR|g" "$BACKUP_DIR/scripts/mysql_realtime_monitor.sh"

chmod +x "$BACKUP_DIR/scripts/mysql_realtime_monitor.sh"
log_info "监控脚本创建完成"

# 创建systemd服务
log_step "创建systemd服务..."

cat > /etc/systemd/system/mysql-realtime-backup.service << EOF
[Unit]
Description=MySQL Real-time Backup Monitor (Optimized)
After=mysqld.service
Requires=mysqld.service

[Service]
Type=simple
User=root
ExecStart=$BACKUP_DIR/scripts/mysql_realtime_monitor.sh start
ExecStop=$BACKUP_DIR/scripts/mysql_realtime_monitor.sh stop
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
log_info "systemd服务创建完成"

# 配置定时任务
log_step "配置定时任务..."

cat > /tmp/optimized_cron << EOF
# MySQL实时备份定时任务（最新优化版）
0 2 * * * $BACKUP_DIR/scripts/mysql_realtime_backup.sh full >/dev/null 2>&1
0 */6 * * * $BACKUP_DIR/scripts/mysql_realtime_backup.sh binlog >/dev/null 2>&1
0 3 * * * $BACKUP_DIR/scripts/mysql_realtime_backup.sh cleanup >/dev/null 2>&1
EOF

crontab /tmp/optimized_cron
log_info "定时任务配置完成"

# 初始化WebDAV环境
if [ "$ENABLE_WEBDAV" = "y" ]; then
    log_step "初始化WebDAV环境..."

    # 测试WebDAV连接
    response=$(curl -s -w "%{http_code}" -o /dev/null \
        --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
        --request PROPFIND \
        --header "Depth: 0" \
        "$WEBDAV_URL" 2>/dev/null)

    if [ "$response" = "207" ] || [ "$response" = "200" ]; then
        log_info "WebDAV连接测试成功"

        # 创建完整的目录结构
        log_info "创建WebDAV目录结构..."
        curl -s --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
             --request MKCOL "$WEBDAV_URL$WEBDAV_REMOTE_DIR/" >/dev/null 2>&1
        curl -s --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
             --request MKCOL "$WEBDAV_URL$WEBDAV_REMOTE_DIR/full/" >/dev/null 2>&1
        curl -s --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
             --request MKCOL "$WEBDAV_URL$WEBDAV_REMOTE_DIR/incremental/" >/dev/null 2>&1
        curl -s --user "$WEBDAV_USERNAME:$WEBDAV_PASSWORD" \
             --request MKCOL "$WEBDAV_URL$WEBDAV_REMOTE_DIR/binlog/" >/dev/null 2>&1
        log_info "WebDAV目录结构创建完成"
    else
        log_warn "WebDAV连接失败，HTTP: $response"
        log_warn "请检查WebDAV配置，备份功能仍可正常使用"
    fi
fi

# 初始化和测试
log_step "初始化备份环境..."
"$BACKUP_DIR/scripts/mysql_realtime_backup.sh" init

log_step "执行首次全量备份..."
"$BACKUP_DIR/scripts/mysql_realtime_backup.sh" full

# 启动服务
log_step "启动服务..."
systemctl enable mysql-realtime-backup
systemctl start mysql-realtime-backup

# 显示部署结果
echo ""
echo "=========================================="
log_info "最新优化版本部署完成！"
echo "=========================================="
echo ""
echo "✨ 新特性："
echo "✅ 全量备份按周几命名 (Monday, Tuesday, ...)"
echo "✅ 每个星期几只保留最新的一个全量备份"
echo "✅ 全量备份完成后立即上传到云端"
echo "✅ 增量备份实时生成并立即上传"
echo "✅ 流量优化，月使用量约20-30MB"
echo ""
echo "📁 备份文件示例："
ls -la "$BACKUP_DIR/mysql/full/" 2>/dev/null
echo ""
echo "🔧 管理命令："
echo "  查看服务状态: systemctl status mysql-realtime-backup"
echo "  手动全量备份: $BACKUP_DIR/scripts/mysql_realtime_backup.sh full"
echo "  手动增量备份: $BACKUP_DIR/scripts/mysql_realtime_backup.sh incremental"
echo "  查看备份状态: $BACKUP_DIR/scripts/mysql_realtime_backup.sh status"
echo ""
echo "📊 日志查看："
echo "  备份日志: tail -f /var/log/mysql_backup.log"
echo "  监控日志: tail -f /var/log/mysql_realtime_monitor.log"
echo "  上传日志: tail -f /var/log/webdav_sync.log"
echo ""
log_info "部署完成！您的魔方财务系统现在拥有最优的实时备份策略！"
