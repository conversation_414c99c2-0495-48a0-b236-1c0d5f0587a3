# MySQL 5.6.50 实时备份系统（含WebDAV同步）

适用于宝塔面板环境的MySQL实时增量备份解决方案，专为魔方财务系统等重要业务系统设计。支持本地备份和WebDAV云端实时同步。

## ✨ 核心特性

### 🔄 实时备份
- **Binlog监控**：实时监控MySQL二进制日志变化
- **增量备份**：只备份变化的数据，节省存储空间
- **自动触发**：检测到数据变化自动执行备份

### ☁️ WebDAV云端同步
- **实时同步**：本地备份文件自动同步到云端
- **多平台支持**：坚果云、OneDrive、Nextcloud、群晖NAS等
- **断点续传**：网络中断后自动重试
- **带宽控制**：可限制上传速度

### � 安全特性
- **专用用户**：使用最小权限的备份专用用户
- **数据压缩**：自动压缩备份文件节省空间
- **加密传输**：HTTPS安全传输到云端

## �🚀 快速部署

### 方案一：完整部署（推荐）

包含MySQL备份 + WebDAV云端同步：

```bash
# 1. 下载完整部署脚本
wget https://your-domain.com/deploy_mysql_backup_with_webdav.sh

# 2. 给脚本执行权限
chmod +x deploy_mysql_backup_with_webdav.sh

# 3. 运行部署脚本
./deploy_mysql_backup_with_webdav.sh
```

### 方案二：仅MySQL备份

如果暂时不需要WebDAV同步：

```bash
# 1. 下载基础部署脚本
wget https://your-domain.com/deploy_mysql_backup.sh

# 2. 给脚本执行权限
chmod +x deploy_mysql_backup.sh

# 3. 运行部署脚本
./deploy_mysql_backup.sh
```

### 配置信息

部署时需要输入：
- **MySQL配置**：root密码、数据库名、备份用户信息
- **备份目录**：本地备份存储路径
- **WebDAV配置**：服务器地址、用户名、密码（可选）

### 手动部署

如果需要手动配置，请参考 `mysql_realtime_backup_guide.md` 文件。

## � 备份策略

### � 自动备份计划
- **全量备份**：每日凌晨2点自动执行
- **增量备份**：检测到数据变化时自动执行
- **Binlog备份**：每30分钟备份一次
- **自动清理**：定期清理过期备份文件

### ☁️ WebDAV同步策略
- **实时同步**：文件变化时立即上传到云端
- **定时全量同步**：每小时检查一次完整性
- **错误重试**：失败时自动重试3次
- **带宽控制**：可限制上传速度避免影响业务

## 🌐 支持的WebDAV服务

| 服务商 | 免费空间 | 推荐指数 | 配置难度 | 备注 |
|--------|----------|----------|----------|------|
| 坚果云 | 3GB | ⭐⭐⭐⭐⭐ | 简单 | 国内访问快，稳定性好 |
| OneDrive | 5GB | ⭐⭐⭐⭐ | 中等 | 需要SharePoint支持 |
| Nextcloud | 自定义 | ⭐⭐⭐⭐ | 中等 | 开源，功能强大 |
| 群晖NAS | 自定义 | ⭐⭐⭐⭐⭐ | 简单 | 本地存储，速度快 |
| ownCloud | 自定义 | ⭐⭐⭐ | 中等 | 企业级解决方案 |

## 📁 目录结构

```
/www/backup/
├── mysql/
│   ├── full/           # 全量备份文件
│   ├── incremental/    # 增量备份文件
│   └── binlog/         # Binlog备份文件
├── scripts/
│   ├── mysql_realtime_backup.sh    # 主备份脚本
│   └── mysql_realtime_monitor.sh   # 实时监控脚本
└── logs/               # 日志文件
```

## 🛠️ 管理命令

### MySQL备份服务管理
```bash
# 查看MySQL备份服务状态
systemctl status mysql-realtime-backup

# 启动/停止/重启MySQL备份服务
systemctl start mysql-realtime-backup
systemctl stop mysql-realtime-backup
systemctl restart mysql-realtime-backup

# 查看MySQL备份服务日志
journalctl -u mysql-realtime-backup -f
```

### WebDAV同步服务管理
```bash
# 查看WebDAV同步服务状态
systemctl status webdav-sync

# 启动/停止/重启WebDAV同步服务
systemctl start webdav-sync
systemctl stop webdav-sync
systemctl restart webdav-sync

# 查看WebDAV同步服务日志
journalctl -u webdav-sync -f
```

### 手动备份操作
```bash
# 执行全量备份
/www/backup/scripts/mysql_realtime_backup.sh full

# 执行增量备份
/www/backup/scripts/mysql_realtime_backup.sh incremental

# 备份Binlog文件
/www/backup/scripts/mysql_realtime_backup.sh binlog

# 清理旧备份
/www/backup/scripts/mysql_realtime_backup.sh cleanup

# 查看备份状态
/www/backup/scripts/mysql_realtime_backup.sh status
```

### WebDAV同步操作
```bash
# 测试WebDAV连接
/www/backup/scripts/webdav_sync.sh test

# 执行一次全量同步
/www/backup/scripts/webdav_sync.sh sync

# 查看同步状态
/www/backup/scripts/webdav_sync.sh status

# 初始化WebDAV环境
/www/backup/scripts/webdav_sync.sh init

# 启动/停止实时同步
/www/backup/scripts/webdav_sync.sh start
/www/backup/scripts/webdav_sync.sh stop
```

### 监控管理
```bash
# 启动实时监控
/www/backup/scripts/mysql_realtime_monitor.sh start

# 停止实时监控
/www/backup/scripts/mysql_realtime_monitor.sh stop

# 查看监控状态
/www/backup/scripts/mysql_realtime_monitor.sh status

# 重启监控
/www/backup/scripts/mysql_realtime_monitor.sh restart
```

## 📊 监控和日志

### 日志文件位置
- **MySQL备份日志**：`/var/log/mysql_backup.log`
- **MySQL监控日志**：`/var/log/mysql_realtime_monitor.log`
- **MySQL错误日志**：`/var/log/mysql_backup_error.log`
- **WebDAV同步日志**：`/var/log/webdav_sync.log`
- **WebDAV错误日志**：`/var/log/webdav_sync_error.log`

### 查看日志
```bash
# 查看MySQL备份日志
tail -f /var/log/mysql_backup.log

# 查看MySQL监控日志
tail -f /var/log/mysql_realtime_monitor.log

# 查看MySQL错误日志
tail -f /var/log/mysql_backup_error.log

# 查看WebDAV同步日志
tail -f /var/log/webdav_sync.log

# 查看WebDAV错误日志
tail -f /var/log/webdav_sync_error.log

# 查看所有相关日志
tail -f /var/log/mysql_*.log /var/log/webdav_*.log
```

## 🔧 配置调优

### 监控频率调整
编辑 `/www/backup/scripts/mysql_realtime_monitor.sh`：
```bash
MONITOR_INTERVAL=30          # 监控间隔（秒）
MAX_BACKUP_FREQUENCY=300     # 最大备份频率（秒）
MIN_CHANGE_SIZE=1024         # 最小变化大小（字节）
```

### 备份保留策略
编辑 `/www/backup/scripts/mysql_realtime_backup.sh`：
```bash
FULL_BACKUP_RETENTION_DAYS=30    # 全量备份保留天数
INCREMENTAL_RETENTION_DAYS=7     # 增量备份保留天数
BINLOG_RETENTION_DAYS=7          # Binlog备份保留天数
```

## 🚨 故障排除

### 常见问题

1. **MySQL连接失败**
   ```bash
   # 检查MySQL服务状态
   systemctl status mysqld
   
   # 检查用户权限
   mysql -u backup_user -p -e "SHOW GRANTS;"
   ```

2. **Binlog未启用**
   ```bash
   # 检查Binlog状态
   mysql -u root -p -e "SHOW VARIABLES LIKE 'log_bin';"
   
   # 查看Binlog文件
   mysql -u root -p -e "SHOW BINARY LOGS;"
   ```

3. **磁盘空间不足**
   ```bash
   # 检查磁盘使用情况
   df -h /www/backup
   
   # 手动清理旧备份
   /www/backup/scripts/mysql_realtime_backup.sh cleanup
   ```

4. **备份文件损坏**
   ```bash
   # 测试备份文件完整性
   gzip -t /www/backup/mysql/full/*.sql.gz

   # 测试SQL文件语法
   mysql --force < backup_file.sql
   ```

5. **WebDAV连接失败**
   ```bash
   # 测试WebDAV连接
   /www/backup/scripts/webdav_sync.sh test

   # 检查网络连通性
   ping your-webdav-server.com

   # 测试端口连通性
   telnet your-webdav-server.com 443
   ```

6. **WebDAV上传失败**
   ```bash
   # 查看WebDAV错误日志
   tail -50 /var/log/webdav_sync_error.log

   # 手动测试上传
   curl -T test_file.txt -u "username:password" https://your-webdav-server.com/dav/test_file.txt

   # 检查WebDAV服务器空间
   # 登录WebDAV服务查看可用空间
   ```

7. **同步延迟过大**
   ```bash
   # 检查inotify监控状态
   ps aux | grep inotifywait

   # 调整同步参数
   vi /www/backup/scripts/webdav_sync.sh
   # 修改 SYNC_INTERVAL 和 BANDWIDTH_LIMIT

   # 重启WebDAV同步服务
   systemctl restart webdav-sync
   ```

### 恢复数据

1. **从全量备份恢复**
   ```bash
   # 解压备份文件
   gunzip full_backup_20231201_020000.sql.gz
   
   # 恢复数据
   mysql -u root -p database_name < full_backup_20231201_020000.sql
   ```

2. **从增量备份恢复**
   ```bash
   # 先恢复全量备份
   mysql -u root -p database_name < full_backup.sql
   
   # 按时间顺序应用增量备份
   mysql -u root -p database_name < incremental_backup_1.sql
   mysql -u root -p database_name < incremental_backup_2.sql
   ```

## 📞 技术支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. MySQL服务状态
3. 磁盘空间使用情况
4. 网络连接状态（远程备份）

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持MySQL 5.6.50
- 实时增量备份功能
- 自动化部署脚本
- systemd服务集成

## 📄 许可证

本项目采用 MIT 许可证。
