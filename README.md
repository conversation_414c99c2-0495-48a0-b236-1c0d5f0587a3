# MySQL 5.6.50 实时备份系统

适用于宝塔面板环境的MySQL实时增量备份解决方案，专为魔方财务系统等重要业务系统设计。

## 🚀 快速部署

### 一键部署（推荐）

```bash
# 1. 下载部署脚本
wget https://your-domain.com/deploy_mysql_backup.sh

# 2. 给脚本执行权限
chmod +x deploy_mysql_backup.sh

# 3. 运行部署脚本
./deploy_mysql_backup.sh
```

按照提示输入以下信息：
- MySQL root密码
- 要备份的数据库名（魔方财务系统数据库）
- 备份用户名和密码
- 备份目录路径
- 是否启用远程备份

### 手动部署

如果需要手动配置，请参考 `mysql_realtime_backup_guide.md` 文件。

## 📋 系统特性

### 🔄 实时备份
- **Binlog监控**：实时监控MySQL二进制日志变化
- **增量备份**：只备份变化的数据，节省存储空间
- **自动触发**：检测到数据变化自动执行备份

### 💾 备份策略
- **全量备份**：每日凌晨2点自动执行
- **增量备份**：检测到变化时自动执行
- **Binlog备份**：每30分钟备份一次
- **自动清理**：定期清理过期备份文件

### 🔐 安全特性
- **专用用户**：使用最小权限的备份专用用户
- **数据压缩**：自动压缩备份文件节省空间
- **远程同步**：支持同步到远程服务器或云存储

## 📁 目录结构

```
/www/backup/
├── mysql/
│   ├── full/           # 全量备份文件
│   ├── incremental/    # 增量备份文件
│   └── binlog/         # Binlog备份文件
├── scripts/
│   ├── mysql_realtime_backup.sh    # 主备份脚本
│   └── mysql_realtime_monitor.sh   # 实时监控脚本
└── logs/               # 日志文件
```

## 🛠️ 管理命令

### 服务管理
```bash
# 查看服务状态
systemctl status mysql-realtime-backup

# 启动/停止/重启服务
systemctl start mysql-realtime-backup
systemctl stop mysql-realtime-backup
systemctl restart mysql-realtime-backup

# 查看服务日志
journalctl -u mysql-realtime-backup -f
```

### 手动备份
```bash
# 执行全量备份
/www/backup/scripts/mysql_realtime_backup.sh full

# 执行增量备份
/www/backup/scripts/mysql_realtime_backup.sh incremental

# 备份Binlog文件
/www/backup/scripts/mysql_realtime_backup.sh binlog

# 清理旧备份
/www/backup/scripts/mysql_realtime_backup.sh cleanup

# 查看备份状态
/www/backup/scripts/mysql_realtime_backup.sh status
```

### 监控管理
```bash
# 启动实时监控
/www/backup/scripts/mysql_realtime_monitor.sh start

# 停止实时监控
/www/backup/scripts/mysql_realtime_monitor.sh stop

# 查看监控状态
/www/backup/scripts/mysql_realtime_monitor.sh status

# 重启监控
/www/backup/scripts/mysql_realtime_monitor.sh restart
```

## 📊 监控和日志

### 日志文件位置
- **备份日志**：`/var/log/mysql_backup.log`
- **监控日志**：`/var/log/mysql_realtime_monitor.log`
- **错误日志**：`/var/log/mysql_backup_error.log`

### 查看日志
```bash
# 查看备份日志
tail -f /var/log/mysql_backup.log

# 查看监控日志
tail -f /var/log/mysql_realtime_monitor.log

# 查看错误日志
tail -f /var/log/mysql_backup_error.log
```

## 🔧 配置调优

### 监控频率调整
编辑 `/www/backup/scripts/mysql_realtime_monitor.sh`：
```bash
MONITOR_INTERVAL=30          # 监控间隔（秒）
MAX_BACKUP_FREQUENCY=300     # 最大备份频率（秒）
MIN_CHANGE_SIZE=1024         # 最小变化大小（字节）
```

### 备份保留策略
编辑 `/www/backup/scripts/mysql_realtime_backup.sh`：
```bash
FULL_BACKUP_RETENTION_DAYS=30    # 全量备份保留天数
INCREMENTAL_RETENTION_DAYS=7     # 增量备份保留天数
BINLOG_RETENTION_DAYS=7          # Binlog备份保留天数
```

## 🚨 故障排除

### 常见问题

1. **MySQL连接失败**
   ```bash
   # 检查MySQL服务状态
   systemctl status mysqld
   
   # 检查用户权限
   mysql -u backup_user -p -e "SHOW GRANTS;"
   ```

2. **Binlog未启用**
   ```bash
   # 检查Binlog状态
   mysql -u root -p -e "SHOW VARIABLES LIKE 'log_bin';"
   
   # 查看Binlog文件
   mysql -u root -p -e "SHOW BINARY LOGS;"
   ```

3. **磁盘空间不足**
   ```bash
   # 检查磁盘使用情况
   df -h /www/backup
   
   # 手动清理旧备份
   /www/backup/scripts/mysql_realtime_backup.sh cleanup
   ```

4. **备份文件损坏**
   ```bash
   # 测试备份文件完整性
   gzip -t /www/backup/mysql/full/*.sql.gz
   
   # 测试SQL文件语法
   mysql --force < backup_file.sql
   ```

### 恢复数据

1. **从全量备份恢复**
   ```bash
   # 解压备份文件
   gunzip full_backup_20231201_020000.sql.gz
   
   # 恢复数据
   mysql -u root -p database_name < full_backup_20231201_020000.sql
   ```

2. **从增量备份恢复**
   ```bash
   # 先恢复全量备份
   mysql -u root -p database_name < full_backup.sql
   
   # 按时间顺序应用增量备份
   mysql -u root -p database_name < incremental_backup_1.sql
   mysql -u root -p database_name < incremental_backup_2.sql
   ```

## 📞 技术支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. MySQL服务状态
3. 磁盘空间使用情况
4. 网络连接状态（远程备份）

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持MySQL 5.6.50
- 实时增量备份功能
- 自动化部署脚本
- systemd服务集成

## 📄 许可证

本项目采用 MIT 许可证。
