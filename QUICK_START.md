# 快速开始指南

本指南帮助您在5分钟内完成MySQL实时备份系统的部署，包含WebDAV云端同步功能。

## 🎯 适用场景

- 宝塔面板 + MySQL 5.6.50
- 魔方财务系统或其他重要业务系统
- 需要实时备份和云端同步

## ⚡ 5分钟快速部署

### 第1步：下载部署脚本（30秒）

```bash
# 下载完整部署脚本
wget https://raw.githubusercontent.com/your-repo/mysql-backup/main/deploy_mysql_backup_with_webdav.sh

# 或者使用curl
curl -O https://raw.githubusercontent.com/your-repo/mysql-backup/main/deploy_mysql_backup_with_webdav.sh

# 给脚本执行权限
chmod +x deploy_mysql_backup_with_webdav.sh
```

### 第2步：运行部署脚本（3分钟）

```bash
# 以root用户运行
sudo ./deploy_mysql_backup_with_webdav.sh
```

### 第3步：按提示输入配置信息（1分钟）

#### MySQL配置
```
MySQL root密码: [输入您的MySQL root密码]
要备份的数据库名: [输入魔方财务系统数据库名，如：mofang_finance]
备份用户名 (默认: backup_user): [直接回车或输入自定义用户名]
备份用户密码: [输入一个强密码]
```

#### 备份目录配置
```
备份目录 (默认: /www/backup): [直接回车使用默认路径]
```

#### WebDAV配置（推荐使用坚果云）
```
是否启用WebDAV同步? (y/n, 默认: y): y
请选择WebDAV服务类型 (1-6): 1  [选择坚果云]
坚果云邮箱: <EMAIL>
坚果云应用密码: [在坚果云生成的应用密码]
WebDAV远程目录名 (默认: mysql_backup): [直接回车]
```

### 第4步：验证部署结果（30秒）

```bash
# 检查服务状态
systemctl status mysql-realtime-backup
systemctl status webdav-sync

# 查看备份日志
tail -f /var/log/mysql_backup.log

# 测试WebDAV连接
/www/backup/scripts/webdav_sync.sh test
```

## 🔧 坚果云WebDAV配置详解

### 获取坚果云应用密码

1. **登录坚果云网页版**
   - 访问：https://www.jianguoyun.com/
   - 使用您的账号登录

2. **进入安全设置**
   - 点击右上角头像 → "账户信息"
   - 选择"安全选项"标签页

3. **生成应用密码**
   - 找到"第三方应用管理"
   - 点击"添加应用密码"
   - 输入应用名称：`MySQL备份系统`
   - 复制生成的应用密码

4. **配置信息**
   ```
   WebDAV地址: https://dav.jianguoyun.com/dav/
   用户名: 您的坚果云邮箱
   密码: 刚才生成的应用密码（不是登录密码）
   ```

## 📊 验证系统运行

### 检查备份功能

```bash
# 1. 查看备份服务状态
systemctl status mysql-realtime-backup

# 2. 手动执行一次全量备份
/www/backup/scripts/mysql_realtime_backup.sh full

# 3. 检查备份文件
ls -la /www/backup/mysql/full/

# 4. 查看备份日志
tail -20 /var/log/mysql_backup.log
```

### 检查WebDAV同步

```bash
# 1. 查看同步服务状态
systemctl status webdav-sync

# 2. 测试WebDAV连接
/www/backup/scripts/webdav_sync.sh test

# 3. 手动执行一次同步
/www/backup/scripts/webdav_sync.sh sync

# 4. 查看同步日志
tail -20 /var/log/webdav_sync.log
```

### 验证实时备份

```bash
# 1. 在数据库中插入测试数据
mysql -u root -p your_database_name -e "INSERT INTO test_table (name) VALUES ('test_backup');"

# 2. 等待30秒，查看是否触发增量备份
tail -f /var/log/mysql_backup.log

# 3. 检查WebDAV是否同步了新文件
tail -f /var/log/webdav_sync.log
```

## 🎉 部署完成后的操作

### 1. 登录坚果云查看备份文件

- 登录坚果云网页版或客户端
- 进入 `mysql_backup` 目录
- 应该能看到以下结构：
  ```
  mysql_backup/
  ├── full/           # 全量备份文件
  ├── incremental/    # 增量备份文件
  └── binlog/         # 二进制日志备份
  ```

### 2. 设置监控告警（可选）

```bash
# 创建监控脚本
cat > /www/backup/scripts/backup_monitor.sh << 'EOF'
#!/bin/bash
# 检查备份状态并发送告警

LOG_FILE="/var/log/mysql_backup.log"
LAST_BACKUP=$(grep "全量备份完成" $LOG_FILE | tail -1)

if [ -z "$LAST_BACKUP" ]; then
    echo "警告：未找到备份记录" | mail -s "备份系统告警" <EMAIL>
fi
EOF

chmod +x /www/backup/scripts/backup_monitor.sh

# 添加到定时任务
echo "0 9 * * * /www/backup/scripts/backup_monitor.sh" | crontab -
```

### 3. 定期检查系统状态

建议每周检查一次：

```bash
# 创建检查脚本
cat > /www/backup/scripts/weekly_check.sh << 'EOF'
#!/bin/bash
echo "=== MySQL备份系统状态检查 ==="
echo "时间: $(date)"
echo ""

echo "1. 服务状态:"
systemctl is-active mysql-realtime-backup
systemctl is-active webdav-sync
echo ""

echo "2. 最近备份:"
ls -lt /www/backup/mysql/full/ | head -3
echo ""

echo "3. 磁盘使用:"
df -h /www/backup
echo ""

echo "4. WebDAV连接测试:"
/www/backup/scripts/webdav_sync.sh test
EOF

chmod +x /www/backup/scripts/weekly_check.sh
```

## 🚨 常见问题快速解决

### 问题1：MySQL连接失败
```bash
# 检查MySQL服务
systemctl status mysqld

# 重置MySQL密码（如果忘记）
# 请参考宝塔面板的MySQL密码重置功能
```

### 问题2：WebDAV连接失败
```bash
# 检查网络连接
ping dav.jianguoyun.com

# 验证用户名密码
curl -u "your_email:your_app_password" https://dav.jianguoyun.com/dav/

# 重新生成坚果云应用密码
```

### 问题3：备份文件过大
```bash
# 启用压缩（默认已启用）
vi /www/backup/scripts/mysql_realtime_backup.sh
# 确认 ENABLE_COMPRESSION=true

# 调整备份保留策略
# 修改 FULL_BACKUP_RETENTION_DAYS 参数
```

### 问题4：同步速度慢
```bash
# 设置带宽限制
vi /www/backup/scripts/webdav_sync.sh
# 修改 BANDWIDTH_LIMIT=500  # 限制为500KB/s

# 或者取消带宽限制
# 修改 BANDWIDTH_LIMIT=0
```

## 📞 获取帮助

如果遇到问题：

1. **查看日志文件**：
   ```bash
   tail -50 /var/log/mysql_backup.log
   tail -50 /var/log/webdav_sync.log
   ```

2. **检查服务状态**：
   ```bash
   systemctl status mysql-realtime-backup
   systemctl status webdav-sync
   ```

3. **重启服务**：
   ```bash
   systemctl restart mysql-realtime-backup
   systemctl restart webdav-sync
   ```

4. **查看详细文档**：
   - `README.md` - 完整使用说明
   - `WEBDAV_GUIDE.md` - WebDAV配置详解
   - `mysql_realtime_backup_guide.md` - 技术详解

## 🎊 恭喜！

您已经成功部署了MySQL实时备份系统！

现在您的魔方财务系统数据将得到以下保护：
- ✅ 实时增量备份
- ✅ 每日全量备份
- ✅ 自动云端同步
- ✅ 多重安全保障

系统将自动运行，无需人工干预。建议定期检查日志确保一切正常运行。
